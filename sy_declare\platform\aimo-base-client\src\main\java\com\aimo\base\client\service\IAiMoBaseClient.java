package com.aimo.base.client.service;

import com.aimo.common.model.ResultBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface IAiMoBaseClient {
    /**
     * 审批流通知接口
     *
     * @param userIds 用户ID列表，逗号分隔
     * @param title 通知标题
     * @param content 通知内容
     * @return 通知发送结果
     */
    @PostMapping(value = "/newApply/noticeUsers")
    ResultBody<Boolean> noticeUsers(@RequestParam(value = "userIds") String userIds,
                                    @RequestParam(value = "title") String title,
                                    @RequestParam(value = "content") String content);

}
