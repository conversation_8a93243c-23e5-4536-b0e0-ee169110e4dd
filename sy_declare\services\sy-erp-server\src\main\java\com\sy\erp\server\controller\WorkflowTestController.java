package com.sy.erp.server.controller;

import com.aimo.common.model.ResultBody;
import com.sy.erp.server.service.WorkflowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 工作流测试控制器
 * 用于测试工作流通知功能
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/workflow/test")
@Api(tags = "工作流测试接口")
public class WorkflowTestController {

    @Autowired
    private WorkflowService workflowService;

    /**
     * 测试通知发送功能
     */
    @PostMapping("/notification")
    @ApiOperation("测试通知发送")
    public ResultBody<String> testNotification(
            @RequestParam String userIds,
            @RequestParam String title,
            @RequestParam String content) {
        
        log.info("测试通知发送: userIds={}, title={}, content={}", userIds, title, content);
        
        try {
            ResultBody<Boolean> result = workflowService.getAimoNotification(userIds, title, content);
            
            if (result != null && result.isOk() && result.getData() != null && result.getData()) {
                return ResultBody.ok("通知发送成功");
            } else {
                String errorMsg = result != null ? result.getMessage() : "未知错误";
                return ResultBody.failed("通知发送失败: " + errorMsg);
            }
            
        } catch (Exception e) {
            log.error("测试通知发送失败", e);
            return ResultBody.failed("通知发送异常: " + e.getMessage());
        }
    }

    /**
     * 检查工作流服务状态
     */
    @GetMapping("/status")
    @ApiOperation("检查工作流服务状态")
    public ResultBody<String> checkStatus() {
        try {
            // 尝试调用一个简单的方法来检查服务状态
            String result = workflowService.getCurrentTaskInf("test", "test", "test");
            return ResultBody.ok("工作流服务正常，测试结果: " + result);
        } catch (Exception e) {
            log.error("工作流服务检查失败", e);
            return ResultBody.failed("工作流服务异常: " + e.getMessage());
        }
    }
}
