package com.aimo.base.server.configuration;

import com.aimo.common.exception.OpenAccessDeniedHandler;
import com.aimo.common.exception.OpenAuthenticationEntryPoint;
import com.aimo.common.security.OpenHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.authentication.BearerTokenExtractor;
import org.springframework.security.oauth2.provider.client.JdbcClientDetailsService;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.security.web.authentication.logout.SimpleUrlLogoutSuccessHandler;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.io.IOException;

/**
 * oauth2资源服务器配置
 *
 * @author: chiYe
 */
@Slf4j
@Configuration
@EnableResourceServer
public class ResourceServerConfiguration extends ResourceServerConfigurerAdapter {
    @Resource
    private RedisConnectionFactory redisConnectionFactory;
    @Resource
    private DataSource dataSource;
    @Resource
    private PasswordEncoder passwordEncoder;


    private RedisTokenStore tokenStore;

    @Bean
    public RedisTokenStore tokenStore() {
        tokenStore = new RedisTokenStore(redisConnectionFactory);
        return tokenStore;
    }

    private final BearerTokenExtractor tokenExtractor = new BearerTokenExtractor();

    @Bean
    public JdbcClientDetailsService clientDetailsService() {
        JdbcClientDetailsService jdbcClientDetailsService = new JdbcClientDetailsService(dataSource);
        jdbcClientDetailsService.setPasswordEncoder(passwordEncoder);
        return jdbcClientDetailsService;
    }


    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
        // 构建redis获取token服务类
        resources.tokenServices(OpenHelper.buildRedisTokenServices(redisConnectionFactory));
    }

    @Override
    public void configure(HttpSecurity http) throws Exception {
        http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
                .and()
                .authorizeRequests()
                // 监控端点内部放行
                .requestMatchers(EndpointRequest.toAnyEndpoint()).permitAll()
                // fegin访问或无需身份认证
                .antMatchers(
                        "/login/token",//登录
                        "/menu/getAll",//主页获取菜单
                        "/dictionary/listValueBy",//根据数据字典编码查询值列表
                        "/dictionary/getValueBy",//根据数据字典编码和值编码查询值
                        "/department/getById",//根据ID查找部门数据
                        "/department/getByUserId",//根据用户ID查找部门数据
                        "/department/getAll",//查询所有部门数据
                        "/company/getAll",//查询所有公司数据
                        "/company/getById",//通过ID获取公司信息数据
                        "/role/getAll",//通过所有角色数据
                        "/file/*",//上传、下载文件接口
                        "/lingNoticeSync/noticeSync",//领星通知
                        "/authority/access",
                        "/authority/app",
                        "/authority/user/grant",
                        "/app/*/info",
                        "/app/client/*/info",
                        "/gateway/api/**",
                        "/gateway/route/path/**",
                        "/declarationBooking/updateDeclarationBooking",//更新领星数据
                        "/user/login",//用户登录
                        "/fbaSendPlanTime/addBoxedTime",
                        "/workflow/*",//工作流相关接口
                        "/newApply/noticeUsers"//工作流通知接口
                ).permitAll()
                .anyRequest().authenticated()
                .and()
                //认证鉴权错误处理,为了统一异常处理。每个资源服务器都应该加上。
                .exceptionHandling()
                .accessDeniedHandler(new OpenAccessDeniedHandler())
                .authenticationEntryPoint(new OpenAuthenticationEntryPoint())
                .and()
                .csrf().disable();
    }

    public class LogoutSuccessHandler extends SimpleUrlLogoutSuccessHandler {
        public LogoutSuccessHandler() {
            // 重定向到原地址
            this.setUseReferer(true);
        }

        @Override
        public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
            try {
                // 解密请求头
                authentication = tokenExtractor.extract(request);
                if (authentication != null && authentication.getPrincipal() != null) {
                    String tokenValue = authentication.getPrincipal().toString();
                    log.debug("revokeToken tokenValue:{}", tokenValue);
                    // 移除token
                    tokenStore.removeAccessToken(tokenStore.readAccessToken(tokenValue));
                }
            } catch (Exception e) {
                log.error("revokeToken error:{0}", e);
            }
            super.onLogoutSuccess(request, response, authentication);
        }
    }
}

