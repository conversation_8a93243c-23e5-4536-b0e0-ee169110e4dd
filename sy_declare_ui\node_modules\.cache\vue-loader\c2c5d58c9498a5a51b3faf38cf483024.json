{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\newApply\\newApply\\newApply\\index.vue?vue&type=style&index=0&id=9bb720fa&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\newApply\\newApply\\newApply\\index.vue", "mtime": 1754279296931}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouc2VhcmNoLWNvbi10b3B7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgcGFkZGluZzogMDsNCiAgLy/moIfnrb7jgIFza3XjgIFhc2lu5pCc57Si6aG5DQogIC5tdWx0aUNsYXNzew0KICAgIC5mbGV4LWh7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgbWFyZ2luLXRvcDoycHg7DQogICAgfQ0KICB9DQp9DQoud2lkdGhDbGFzcyB7DQogIHdpZHRoOiAzNTBweA0KfQ0KLmJ1dHRvbk1hcmdpbnsNCiAgbWFyZ2luLWxlZnQ6MTVweDsNCn0NCi5zYWxlc1Jhbmsgew0KICAucmVwb3J0VGFibGUgew0KICAgIC5yZXF1ZXN0RGV0YWlsQ29sdW1uIHsNCiAgICAgIC5pdnUtdGFibGUtY2VsbCB7DQogICAgICAgIHBhZGRpbmctbGVmdDogMnB4Ow0KICAgICAgICBwYWRkaW5nLXJpZ2h0OiAycHg7DQoNCiAgICAgICAgLnJlcXVlc3REZXRhaWxUZCB7DQogICAgICAgICAgcGFkZGluZzogMCAwOw0KDQogICAgICAgICAgLml2dS1yb3cgew0KICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGVhZWM7DQoNCiAgICAgICAgICAgICY6bGFzdC1jaGlsZCB7DQogICAgICAgICAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC5pdnUtY29sIHsNCiAgICAgICAgICAgICAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2U4ZWFlYzsNCiAgICAgICAgICAgICAgcGFkZGluZzogM3B4IDJweDsNCiAgICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQoNCiAgICAgICAgICAgICAgJjpsYXN0LWNoaWxkIHsNCiAgICAgICAgICAgICAgICBib3JkZXItcmlnaHQ6IG5vbmU7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA0tBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/newApply/newApply/newApply", "sourcesContent": ["<!--\r\n@create date 2025-02-07\r\n@desc 上新管理\r\n-->\r\n<template>\r\n  <div class=\"search-con-top salesRank\">\r\n    <Card :shadow=\"true\">\r\n      <div>\r\n        <Form ref=\"searchFormRef\" class=\"searchForm\" :model=\"searchForm\" :inline=\"true\" @submit.native.prevent>\r\n          <FormItem prop=\"date\">\r\n            <DatePicker type=\"daterange\" v-model=\"date\" placement=\"bottom-start\" @on-change=\"dateChange\"\r\n                        placeholder=\"申请开始日期-申请结束日期\" style=\"width: 200px\"></DatePicker>\r\n          </FormItem>\r\n          <FormItem prop=\"shop\">\r\n            <ShopSelect v-model=\"searchForm.shops\" placeholder=\"选择店铺\" width=\"205px\" valueField=\"id\" :isOverseas=\"true\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"sellers\" class=\"sellerSelectItem\">\r\n            <Select multiple type=\"text\" v-model=\"searchForm.sellers\" placeholder=\"销售员\" filterable :max-tag-count=\"1\" style=\"width: 233px\" :transfer=\"true\" >\r\n              <Option v-for=\"item in sellerArr\" :value=\"item.id\" :key=\"item.id\">{{ item.nickName }}</Option>\r\n            </Select>\r\n            <Button type=\"dashed\" @click=\"openPerson\" style=\"margin-left: 3px\" size=\"default\">选择</Button>\r\n            <person-select :visible=\"personVisible\" :onCancel=\"()=>personVisible=false\"\r\n                           @setPerson=\"arr => (searchForm.sellers = arr.map(v => v.id))\" @setSelectInfo=\"setSelectInfo\"\r\n                           ref=\"personSelectRef\" groupName=\"operations_persons\" :multiple=\"true\" :isQuery=\"true\" />\r\n          </FormItem>\r\n          <FormItem prop=\"spu\">\r\n            <SpuSelect v-model=\"searchForm.spus\" placeholder=\"选择型号\" width=\"205px\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"sellerSku\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入销售SKU(回车分隔)\" @changeValue=\"(values)=>{ multiValuesSellerSku = values || []; }\" ref=\"multipleRefSellerSkuRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleSellerSku=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleSellerSku\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentSellerSku\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownSellerSku\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"productCode\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品编码(回车分隔)\" @changeValue=\"(values)=>{ multiValuesProductCode = values || []; }\" ref=\"multipleRefProductCodeRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleProductCode=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleProductCode\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentProductCode\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownProductCode\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"status\" :clear=\"true\">\r\n            <Select type=\"text\" v-model=\"searchForm.status\" placeholder=\"状态\" style=\"width:160px\">\r\n              <Option v-for=\"(item,index) in statusList\" :value=\"item.key\" :key=\"index\">{{ item['value'] }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>&nbsp;\r\n            <Button @click=\"handleReset()\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </div>\r\n      <div style=\"margin-bottom: 10px;\" class=\"reportTable\">\r\n        <Button @click=\"addNewApply\" type=\"primary\"  :loading=\"loading\">上新申请</Button>\r\n        <div style=\"display:inline-block\">\r\n          <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                  :on-success=\"handleImportSuccess\"\r\n                  :format=\"['xls', 'xlsx']\"\r\n                  :show-upload-list=\"false\"\r\n                  :on-format-error=\"handleImportFormatError\"\r\n                  :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n            <Button  class=\"buttonMargin\" :loading=\"loading\">上传文件</Button>\r\n          </Upload></div>\r\n        <Button @click=\"downTemplate\" class=\"buttonMargin\" :loading=\"loading\">下载模板</Button>\r\n        <Button @click=\"noticeSellerId\" class=\"buttonMargin\" :loading=\"loading\">通知运营</Button>\r\n        <Button @click=\"refreshStatus\" class=\"buttonMargin\" :loading=\"loading\">检测生成</Button>\r\n        <Button @click=\"handleExport\" class=\"buttonMargin\" :loading=\"loading\">导出</Button>\r\n        <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n               :data=\"tableData\" :loading=\"loading\" @on-selection-change=\"onSelectChange\">\r\n          <template v-slot:status=\"{row}\">\r\n            <span v-for=\"(item, index) in statusList\" :key=\"index\" v-copytext=\"item['value']\"\r\n                        v-if=\"item['key'] === row['status']\">{{ item['value'] }}</span>\r\n          </template>\r\n          <template v-slot:isUpc=\"{row}\">\r\n            <span v-for=\"(item, index) in yesNoOps\" :key=\"index\" v-copytext=\"item['name']\"\r\n                  v-if=\"item['key'] === row['isUpc']\">{{ item['name'] }}</span>\r\n          </template>\r\n          <template v-slot:reviewStatus=\"{row}\">\r\n            <Tag :color=\"getReviewStatusColor(row.reviewFlag)\">\r\n              {{ getReviewStatusText(row.reviewFlag) }}\r\n            </Tag>\r\n          </template>\r\n          <template v-slot:review=\"{row}\">\r\n            <Button\r\n              v-if=\"row.runReviewStatus === 1\"\r\n              type=\"success\"\r\n              size=\"small\"\r\n              @click=\"handleReview(row)\"\r\n              :loading=\"row.reviewLoading\">\r\n              审核\r\n            </Button>\r\n          </template>\r\n          <template v-slot:detailList=\"{ row }\">\r\n            <div class=\"requestDetailTd\">\r\n              <Row>\r\n                <i-col span=\"7\">销售SKU</i-col>\r\n                <i-col span=\"3\">产品编码</i-col>\r\n                <i-col span=\"6\">产品规格</i-col>\r\n                <i-col span=\"4\">UPC码</i-col>\r\n                <i-col span=\"4\">是否刊登</i-col>\r\n              </Row>\r\n              <Row v-for=\"(item, index) in (row.detailList ? row.detailList.filter((_, i) => i < 3) : [])\" :key=\"index\">\r\n                <i-col span=\"7\" v-copytext=\"item['sellerSku']\" :title=\"item['sellerSku']\" style=\"padding: 3px 0;\">{{item['sellerSku']}}</i-col>\r\n                <i-col span=\"3\" v-copytext=\"item['productCode']\" :title=\"item['productCode']\">{{ item['productCode'] }}</i-col>\r\n                <i-col span=\"6\" v-copytext=\"item['productSpec']\" :title=\"item['productSpec']\">{{ item['productSpec'] }}</i-col>\r\n                <i-col span=\"4\" v-copytext=\"item['upcCode']\" :title=\"item['upcCode']\">{{ item['upcCode'] }}</i-col>\r\n                <i-col span=\"4\" v-copytext=\"item['status']===1?'已刊登':'未刊登'\">{{ item['status']===1?'已刊登':'未刊登' }}</i-col>\r\n              </Row>\r\n              <div style=\"text-align: right; padding-top: 5px; padding-right: 5px\"\r\n                   v-if=\"row.detailList && row.detailList.length > 0\">\r\n                <Poptip\r\n                  title=\"明细信息\"\r\n                  content=\"content\"\r\n                  placement=\"left-end\"\r\n                  trigger=\"click\"\r\n                  :transfer=\"true\"\r\n                  :max-width=\"550\">\r\n                  <a @click=\"clickMore(row)\">查看更多</a>\r\n                  <div slot=\"content\" style=\"padding-bottom: 8px; max-height: 500px\">\r\n                    <Table\r\n                      :columns=\"popColumns\"\r\n                      :border=\"true\"\r\n                      :data=\"row.detailList || []\"\r\n                      size=\"small\"\r\n                      :max-height=\"420\"/>\r\n                  </div>\r\n                </Poptip>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <template v-slot:action=\"{ row }\">\r\n            <div>\r\n              <template v-for=\"(item, index) in getFilteredButtons(row).length > 3? getFilteredButtons(row).slice(0, 2): getFilteredButtons(row)\">\r\n                <a v-if=\"!isButtonDisabled(item, row)\"\r\n                   @click=\"item.function(row)\"\r\n                   style=\"margin-right: 10px\"\r\n                   :title=\"item.title\"\r\n                   :key=\"'btn-' + index\">{{item.text }}</a>\r\n                <span v-else\r\n                      style=\"margin-right: 10px; color: #c5c8ce; cursor: not-allowed;\"\r\n                      :title=\"item.title + '(已禁用)'\"\r\n                      :key=\"'disabled-btn-' + index\">{{item.text }}</span>\r\n              </template>\r\n              <Dropdown :transfer=\"true\" ref=\"dropdownRef\" @on-click=\"handleDropDownClick($event, row)\" transfer-class-name=\"inventory-manage-down-btns\" v-if=\"getFilteredButtons(row).length > 3\">\r\n                <a href=\"javascript:void(0)\"><span>更多</span><Icon type=\"ios-arrow-down\"></Icon></a>\r\n                <DropdownMenu slot=\"list\">\r\n                  <DropdownItem v-for=\"(item, index) in getFilteredButtons(row).slice(2)\"\r\n                                :name=\"item.type\"\r\n                                :key=\"index\"\r\n                                :disabled=\"isButtonDisabled(item, row)\">{{item.text}}</DropdownItem>\r\n                </DropdownMenu>\r\n              </Dropdown>\r\n            </div>\r\n          </template>\r\n        </Table>\r\n        <Page :total=\"searchForm.total\" size=\"small\" :current=\"searchForm.page\" :page-size=\"searchForm.limit\" :show-elevator=\"true\"\r\n              :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n      </div>\r\n      <NewApplyEdit ref=\"newApplyEditRef\" :visible=\"editVisible\" :title=\"editTitle\" :onCancel=\"(value)=>{this.editVisible=false;if(value){this.handleSearch()}}\"></NewApplyEdit>\r\n\r\n      <!-- 审核弹窗 -->\r\n      <Modal\r\n        v-model=\"reviewModalVisible\"\r\n        title=\"审核申请\"\r\n        :mask-closable=\"false\"\r\n        :closable=\"false\"\r\n        width=\"500\">\r\n        <Form ref=\"reviewForm\" :model=\"reviewForm\" :rules=\"reviewFormRules\" :label-width=\"80\">\r\n          <FormItem label=\"型号\">\r\n            <span>{{ reviewForm.spu }}</span>\r\n          </FormItem>\r\n          <FormItem label=\"审核结果\" prop=\"agree\">\r\n            <RadioGroup v-model=\"reviewForm.agree\">\r\n              <Radio label=\"true\">通过</Radio>\r\n              <Radio label=\"false\">驳回</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"审核意见\" prop=\"comment\">\r\n            <Input\r\n              v-model=\"reviewForm.comment\"\r\n              type=\"textarea\"\r\n              :autosize=\"{minRows: 3, maxRows: 6}\"\r\n              placeholder=\"请输入审核意见\"\r\n              maxlength=\"200\"\r\n              show-word-limit />\r\n          </FormItem>\r\n        </Form>\r\n        <div slot=\"footer\">\r\n          <Button @click=\"handleReviewCancel\">取消</Button>\r\n          <Button type=\"primary\" @click=\"handleReviewSubmit\" :loading=\"reviewLoading\">确定</Button>\r\n        </div>\r\n      </Modal>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {autoTableHeight, isEmpty} from \"@/libs/tools.js\";\r\nimport ShopSelect from \"@/components/shopSelect/index.vue\";\r\nimport SpuSelect from \"@/components/spu/index.vue\";\r\nimport Multiple from \"@/view/module/common/multipleInput.vue\";\r\nimport personSelect from \"_c/person-select-radio/index.vue\";\r\nimport NewApply from \"@/api/newApply/newApply\";\r\nimport Workflow from \"@/api/base/workflow\";\r\nimport NewApplyEdit from \"./edit.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\nexport default {\r\n  name: \"newApply\",\r\n  components: {personSelect, Multiple, ShopSelect,SpuSelect,NewApplyEdit},\r\n  data() {\r\n    const buttons = [{type: \"look\", text: \"查看\", title: \"点击查看\", function: this.lookData},\r\n                    {type: \"edit\", text: \"修改\", title: \"点击修改\", function: this.editData},\r\n                    {type: \"repeal\", text: \"作废\", title: \"点击作废\", function: this.delData},\r\n                    {type: \"remove\", text: \"删除\", title: \"点击删除\", function: this.repealData},]\r\n    return {\r\n      autoTableHeight,\r\n      buttons,\r\n      loading: false,\r\n      sellerArr:[],\r\n      currentRow:null,\r\n      personVisible:false,\r\n      yesNoOps:[{ key:1, name: \"是\" }, { key:0, name: \"否\" }],\r\n      multiValuesSellerSku:[],\r\n      popVisibleSellerSku:false,\r\n      popContentSellerSku: undefined,\r\n      multiValuesProductCode:[],\r\n      popVisibleProductCode:false,\r\n      popContentProductCode: undefined,\r\n      importURl: getUrl() + NewApply.path+ '/importFile',\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      selectData:[],\r\n      statusList:[{\"key\":-1,\"value\":\"全部\"},{\"key\":0,\"value\":\"已申请\"},{\"key\":1,\"value\":\"已刊登\"},{\"key\":2,\"value\":\"已作废\"},{\"key\":3,\"value\":\"部分刊登\"}],\r\n      date:[],\r\n      searchForm: {\r\n        startDate:null,\r\n        endDate:null,\r\n        shops: [],\r\n        spus:[],\r\n        status:null,\r\n        page:1,\r\n        limit:10\r\n      },\r\n      tableData: [], //表格数据\r\n      columns: [{type: 'selection',width: 55,},\r\n        {title: '申请日期',key: 'sheetDate',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['sheetDate']}>{row['sheetDate']}</span>)},\r\n        {title: '店铺',key: 'shopName',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['shopName']}>{row['shopName']}</span>)},\r\n        {title: '型号',key: 'spu',minWidth: 150,resizable:true,render: (_, { row }) => (<span v-copytext={row['spu']}>{row['spu']}</span>)},\r\n        {title: '销售人员',key: 'sellerName',minWidth: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['sellerName']}>{row['sellerName']}</span>)},\r\n        {title: '是否需要UPC',key: 'isUpc',minWidth: 120,resizable:true,slot:'isUpc'},\r\n        {title: '审核阶段',key: 'review',minWidth: 100,resizable:true,slot:'review'},\r\n        {title: '运输模式',key: 'fulfillmentType',minWidth: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['fulfillmentType']}>{row['fulfillmentType']}</span>)},\r\n        {title: 'Listing简称',key: 'listingTitle',minWidth: 200,resizable:true,render: (_, { row }) => (<span v-copytext={row['listingTitle']}>{row['listingTitle']}</span>)},\r\n        {title: 'sku定位',key: 'skuLoc',minWidth: 200,resizable:true,render: (_, { row }) => (<span v-copytext={row['skuLoc']}>{row['skuLoc']}</span>)},\r\n        {title: \"明细\", align: \"center\", minWidth: 400, key: \"detailList\",slot: 'detailList', className: \"requestDetailColumn\"},\r\n        {title: '使用状态',key: 'status',minWidth: 100,resizable:true,slot:'status'},\r\n        {title: '审批状态',key: 'reviewFlag',minWidth: 100,resizable:true,slot:'reviewStatus'},\r\n        {title: '创建人',key: 'createUserName',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['createUserName']}>{row['createUserName']}</span>)},\r\n        {title: '创建时间',key: 'createTime',width: 160,resizable:true,render:(_, { row }) => (<span v-copytext={row['createTime']}>{row['createTime']}</span>)},\r\n        {title: '操作',key: 'operate',width: 200,resizable:true,slot:\"action\"},\r\n      ],\r\n      popColumns: [\r\n        {title: \"销售SKU\", align: \"center\", minWidth: 300, key: \"sellerSku\",},\r\n        {title: \"产品编码\", align: \"center\", minWidth: 150, key: \"productCode\",},\r\n        {title: \"产品规格\", align: \"center\", minWidth: 300, key: \"productSpec\",},\r\n        {title: \"UPC码\", align: \"center\", minWidth: 150, key: \"upcCode\",},\r\n        {\r\n          title: \"是否刊登\",\r\n          align: \"center\",\r\n          minWidth: 150,\r\n          key: \"status\",\r\n          resizable: true,\r\n          render: (_, { row }) => {\r\n            const statusText = row.status === 1 ? '已刊登' : '未刊登';\r\n            return <span v-copytext={statusText}>{statusText}</span>;\r\n          }\r\n        }\r\n      ],\r\n      editVisible:false,\r\n      editTitle:\"上新管理-新增\",\r\n      // 审核弹窗相关\r\n      reviewModalVisible: false,\r\n      reviewLoading: false,\r\n      reviewForm: {\r\n        id: null,\r\n        spu: '',\r\n        agree: 'true', // 使用字符串，对应Radio的label\r\n        comment: ''\r\n      },\r\n      reviewFormRules: {\r\n        comment: [\r\n          { required: true, message: '请输入审核意见', trigger: 'blur' },\r\n          { min: 1, max: 200, message: '审核意见长度在1到200个字符', trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  //组件初始化进行的操作\r\n  mounted() {\r\n    this.handleSearch();\r\n  },\r\n  methods: {\r\n    handleImportError (err, file) {\r\n      this.loading=false;\r\n      this.$Message.error(file.message);\r\n    },\r\n    handleImportSuccess (res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res.code === 0) {\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.error(res.message);\r\n      }\r\n    },\r\n    handleMaxSize () {\r\n      this.$Message.warning('大小不能超过10M.');\r\n    },\r\n    handleImportFormatError (file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    openPerson(){\r\n      this.resetMultiple();\r\n      //打开人员选择\r\n      const { personSelectRef } = this.$refs;\r\n      const { sellerArr, searchForm } = this;\r\n      const selectedIds = searchForm.sellers || [];\r\n      if (personSelectRef) personSelectRef.setDefault(\r\n        sellerArr.filter(v => selectedIds.includes(v.id)).map(v=>({ name: v.nickName, id: v.id }))\r\n      );//给组件设置默认选中\r\n      this.personVisible = true;\r\n    },\r\n    // 人员选择相关\r\n    setSelectInfo(info={}){\r\n      this.sellerArr = info.personArr || [];\r\n    },\r\n    clickMore(row = {}) {\r\n      this.currentRow = {...row};\r\n    },\r\n    //操作\r\n    handleDropDownClick(name, row = {}) {\r\n      this.clickRow = { ...row };\r\n      // 检查按钮是否被禁用\r\n      const button = this.buttons.find(btn => btn.type === name);\r\n      if (button && this.isButtonDisabled(button, row)) {\r\n        this.$Message.warning('当前状态下该操作不可用');\r\n        return;\r\n      }\r\n\r\n      switch (name) {\r\n        case \"look\":\r\n          this.lookData(row);\r\n          break;\r\n        case \"edit\":\r\n          this.editData(row);\r\n          break;\r\n        case \"remove\":\r\n          this.delData(row);\r\n          break;\r\n        case \"repeal\":\r\n          this.repealData(row);\r\n          break;\r\n        default:\r\n      }\r\n    },\r\n    // 获取过滤后的按钮列表\r\n    getFilteredButtons(row) {\r\n      return this.buttons;\r\n    },\r\n    // 判断按钮是否应该被禁用\r\n    isButtonDisabled(button, row) {\r\n      // 当reviewFlag为1时，禁用修改、作废、删除按钮\r\n      if (row.reviewFlag === 1 && row.status !== 5) {\r\n        // , 'remove'\r\n        return ['edit', 'repeal'].includes(button.type);\r\n      }\r\n      return false;\r\n    },\r\n    // 获取审批状态文本\r\n    getReviewStatusText(reviewFlag) {\r\n      switch (reviewFlag) {\r\n        case 0:\r\n          return '无需审批';\r\n        case 1:\r\n          return '需要审';\r\n        default:\r\n          return '未知状态';\r\n      }\r\n    },\r\n    // 获取审批状态颜色\r\n    getReviewStatusColor(reviewFlag) {\r\n      switch (reviewFlag) {\r\n        case 0:\r\n          return 'default';\r\n        case 1:\r\n          return 'orange';\r\n        default:\r\n          return 'default';\r\n      }\r\n    },\r\n    editData(row){\r\n      // 检查是否被禁用\r\n      if (row.reviewFlag === 1) {\r\n        this.$Message.warning('当前记录正在审批流程中，无法修改');\r\n        return;\r\n      }\r\n      this.editVisible = true;\r\n      const { newApplyEditRef } = this.$refs;\r\n      if (newApplyEditRef) newApplyEditRef.setDefault(row,'Edit');//给组件\r\n    },\r\n    lookData(row){\r\n      this.editVisible = true;\r\n      const { newApplyEditRef } = this.$refs;\r\n      if (newApplyEditRef) newApplyEditRef.setDefault(row,'View');//给组件\r\n    },\r\n    delData(row){\r\n      // 检查是否被禁用\r\n      // if (row.reviewFlag === 1) {\r\n      //   this.$Message.warning('当前记录正在审批流程中，无法删除');\r\n      //   return;\r\n      // }\r\n      this.loading=true;\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这些数据吗？',\r\n        onOk: () => {\r\n          NewApply.remove({\"ids\":row.id}).then(res=>{\r\n            if(res && res['code'] === 0){\r\n              this.handleSearch();\r\n            }else{\r\n              this.$message.error(res['message']);\r\n            }\r\n          }).finally(()=>{this.loading=false;})\r\n        },\r\n      })\r\n    },\r\n    repealData(row){\r\n      // 检查是否被禁用\r\n      if (row.reviewFlag === 1) {\r\n        this.$Message.warning('当前记录正在审批流程中，无法作废');\r\n        return;\r\n      }\r\n      this.loading=true;\r\n      NewApply.repeal({\"ids\":row.id}).then(res=>{\r\n        if(res && res['code'] === 0){\r\n          this.handleSearch();\r\n        }else{\r\n          this.$message.error(res['message']);\r\n        }\r\n      }).finally(()=>{this.loading=false;})\r\n    },\r\n    closeDropdownSellerSku() { //关闭输入文本框\r\n      const { popContentSellerSku } = this;\r\n      const { multipleRefSellerSkuRef } = this.$refs;\r\n      this.popVisibleSellerSku = false;\r\n      if(!popContentSellerSku) return;\r\n      const content = popContentSellerSku ? popContentSellerSku.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesSellerSku = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesSellerSku = [...new Set(this.multiValuesSellerSku)];\r\n      if(multipleRefSellerSkuRef && multipleRefSellerSkuRef.setValueArray){\r\n        multipleRefSellerSkuRef.setValueArray(this.multiValuesSellerSku);\r\n      }\r\n    },\r\n    closeDropdownProductCode() { //关闭输入文本框\r\n      const { popContentProductCode } = this;\r\n      const { multipleRefProductCodeRef } = this.$refs;\r\n      this.popVisibleProductCode = false;\r\n      if(!popContentProductCode) return;\r\n      const content = popContentProductCode ? popContentProductCode.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesProductCode = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesProductCode = [...new Set(this.multiValuesProductCode)];\r\n      if(multipleRefProductCodeRef && multipleRefProductCodeRef.setValueArray){\r\n        multipleRefProductCodeRef.setValueArray(this.multiValuesProductCode);\r\n      }\r\n    },\r\n    getParam(){\r\n      const params = {\r\n        ...this.searchForm\r\n      };\r\n      delete params.shops;\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\"&#&\") : undefined;\r\n      if (this.multiValuesSellerSku.length > 0){\r\n        params[\"sellerSkus\"] = getStr(this.multiValuesSellerSku);\r\n      }\r\n      if (this.multiValuesProductCode.length > 0){\r\n        params[\"productCodes\"] = getStr(this.multiValuesProductCode);\r\n      }\r\n      params[\"sellerIds\"] = getStr(this.searchForm.sellers);\r\n      params[\"shopIds\"] = getStr(this.searchForm.shops);\r\n      params[\"spus\"] = getStr(this.searchForm.spus);\r\n      return params;\r\n    },\r\n    dateChange(date) {\r\n      if (isEmpty(date)) {\r\n        this.searchForm.startDate = '';\r\n        this.searchForm.endDate = '';\r\n      } else {\r\n        this.searchForm.startDate = date[0];\r\n        this.searchForm.endDate = date[1];\r\n      }\r\n    },\r\n    //查询\r\n    handleSearch() {\r\n      const params = this.getParam();\r\n      this.loading = true\r\n      NewApply.listPage(params).then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.tableData = res.data.records;\r\n          this.searchForm.total = parseInt(res.data.total);\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    handleReset() {\r\n      //重置验证\r\n      this.$refs['searchFormRef'].resetFields();\r\n      this.searchForm.shops=[];\r\n      this.searchForm.spus=[];\r\n      this.searchForm.startDate=null;\r\n      this.searchForm.endDate=null;\r\n      this.date = [];\r\n      this.resetMultiple(true);\r\n    },\r\n    resetMultiple(clearTxt = false) {\r\n      if (clearTxt === true) {\r\n        this.multiValuesSellerSku = [];\r\n        const { multipleRefSellerSkuRef } = this.$refs;\r\n        if (multipleRefSellerSkuRef && multipleRefSellerSkuRef.setValueArray) {\r\n          multipleRefSellerSkuRef.setValueArray([]);\r\n        }\r\n        this.multiValuesProductCode = [];\r\n        const { multipleRefProductCodeRef } = this.$refs;\r\n        if (multipleRefProductCodeRef && multipleRefProductCodeRef.setValueArray) {\r\n          multipleRefProductCodeRef.setValueArray([]);\r\n        }\r\n      }\r\n      this.popContentSellerSku = undefined;\r\n      this.popVisibleSellerSku = false;\r\n      this.popContentProductCode = undefined;\r\n      this.popVisibleProductCode = false;\r\n    },\r\n    handlePage(current) {\r\n      this.searchForm.page = current\r\n      this.handleSearch()\r\n    },\r\n    handlePageSize(size) {\r\n      this.searchForm.limit = size\r\n      this.handleSearch()\r\n    },\r\n    onSelectChange(selection){\r\n      this.selectData = selection;\r\n    },\r\n    downTemplate() {\r\n      this.loading = true;\r\n      let params = {};\r\n      params['fileName'] = \"上新申请导入模板.xls\";\r\n      NewApply.downTemplate(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    handleDel(){\r\n      let ids = this.selectData.map(item => item['id']).join(',');\r\n      if (!ids) {\r\n        this.$message.error(\"请选择需要作废的记录\");\r\n        return;\r\n      }\r\n      let sellerSkuUsed = this.selectData.filter(item => item['status'] >= 2).map(item=>item['sellerSku']).join(',');\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这些数据吗？'+(!!sellerSkuUsed?(\"并且存在已匹配的销售SKU\"+sellerSkuUsed):\"\"),\r\n        onOk: () => {\r\n          this.loading = true;\r\n          NewApply.remove({ids: ids}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.$Message.success('删除成功!');\r\n              this.handleSearch();\r\n            } else {\r\n              this.$Message.error(res['message']);\r\n            }\r\n          }).finally(()=>this.loading=false)\r\n        },\r\n      })\r\n    },\r\n    handleExport(){\r\n      this.loading = true;\r\n      let params = this.getParam();\r\n      params['fileName'] = \"上新申请\" + new Date().getTime() + \".xls\";\r\n      NewApply.exportFile(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    addNewApply(){\r\n      this.editVisible=true;\r\n      const { newApplyEditRef } = this.$refs;\r\n      if (newApplyEditRef) newApplyEditRef.setDefault(null,'Add');//给组件\r\n    },\r\n    noticeSellerId(){\r\n      let ids = this.selectData.map(item => item['id']).join(',');\r\n      if (!ids) {\r\n        this.$message.error(\"请选择需要通知运营的记录\");\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      NewApply.noticeSeller({ids: ids}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.$Message.success('通知成功!');\r\n        } else {\r\n          this.$Message.error(res['message']);\r\n        }\r\n      }).finally(()=>this.loading=false)\r\n    },\r\n    refreshStatus(){\r\n      let ids = this.selectData.map(item => item['id']).join(',');\r\n      this.loading = true;\r\n      NewApply.refreshStatus({ids: ids}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.$Message.success('运行成功!');\r\n        } else {\r\n          this.$Message.error(res['message']);\r\n        }\r\n      }).finally(()=>this.loading=false)\r\n    },\r\n    // 审批申请处理\r\n    handleApply(row) {\r\n      this.$Modal.confirm({\r\n        title: '确认申请',\r\n        content: `确定要为型号 \"${row.spu}\" 申请审批吗？`,\r\n        onOk: () => {\r\n          this.$set(row, 'reviewLoading', true);\r\n          // 这里调用审批申请的API\r\n          NewApply.apply({ id: row.id }).then(res => {\r\n            if (res && res.code === 0) {\r\n              this.$Message.success('审批申请提交成功！');\r\n              this.handleSearch(); // 刷新列表\r\n            } else {\r\n              this.$Message.error(res.message || '审批申请失败');\r\n            }\r\n          }).catch(err => {\r\n            this.$Message.error('审批申请失败：' + (err.message || '网络错误'));\r\n          }).finally(() => {\r\n            this.$set(row, 'reviewLoading', false);\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 审批审核处理\r\n    handleReview(row) {\r\n      // 显示审核弹窗\r\n      this.showReviewModal(row);\r\n    },\r\n\r\n    showReviewModal(row) {\r\n      this.reviewForm = {\r\n        id: row.id,\r\n        spu: row.spu,\r\n        agree: 'true', // 默认选择通过，使用字符串\r\n        comment: '' // 审核意见\r\n      };\r\n      this.reviewModalVisible = true;\r\n    },\r\n\r\n    handleReviewSubmit() {\r\n      this.$refs.reviewForm.validate((valid) => {\r\n        if (valid) {\r\n          this.reviewLoading = true;\r\n          const params = {\r\n            id: this.reviewForm.id,\r\n            variables: {\r\n              agree: this.reviewForm.agree === 'true', // 转换为布尔值\r\n              comment: this.reviewForm.comment\r\n            }\r\n          };\r\n\r\n          Workflow.review(params).then(res => {\r\n            if (res && res.code === 0) {\r\n              this.$Message.success('审批审核完成！');\r\n              this.reviewModalVisible = false;\r\n              this.handleSearch(); // 刷新列表\r\n            } else {\r\n              this.$Message.error(res.message || '审批审核失败');\r\n            }\r\n          }).catch(err => {\r\n            this.$Message.error('审批审核失败：' + (err.message || '网络错误'));\r\n          }).finally(() => {\r\n            this.reviewLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    handleReviewCancel() {\r\n      this.reviewModalVisible = false;\r\n      this.reviewForm = {\r\n        id: null,\r\n        spu: '',\r\n        agree: 'true', // 使用字符串\r\n        comment: ''\r\n      };\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.search-con-top{\r\n  position: relative;\r\n  padding: 0;\r\n  //标签、sku、asin搜索项\r\n  .multiClass{\r\n    .flex-h{\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-top:2px;\r\n    }\r\n  }\r\n}\r\n.widthClass {\r\n  width: 350px\r\n}\r\n.buttonMargin{\r\n  margin-left:15px;\r\n}\r\n.salesRank {\r\n  .reportTable {\r\n    .requestDetailColumn {\r\n      .ivu-table-cell {\r\n        padding-left: 2px;\r\n        padding-right: 2px;\r\n\r\n        .requestDetailTd {\r\n          padding: 0 0;\r\n\r\n          .ivu-row {\r\n            border-bottom: 1px solid #e8eaec;\r\n\r\n            &:last-child {\r\n              border-bottom: none;\r\n            }\r\n\r\n            .ivu-col {\r\n              border-right: 1px solid #e8eaec;\r\n              padding: 3px 2px;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n\r\n              &:last-child {\r\n                border-right: none;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}