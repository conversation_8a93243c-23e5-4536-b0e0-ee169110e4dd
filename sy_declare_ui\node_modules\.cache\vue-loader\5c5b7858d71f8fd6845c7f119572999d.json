{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\newApply\\newApply\\newApply\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\newApply\\newApply\\newApply\\index.vue", "mtime": 1754279296931}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAwNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/newApply/newApply/newApply", "sourcesContent": ["<!--\r\n@create date 2025-02-07\r\n@desc 上新管理\r\n-->\r\n<template>\r\n  <div class=\"search-con-top salesRank\">\r\n    <Card :shadow=\"true\">\r\n      <div>\r\n        <Form ref=\"searchFormRef\" class=\"searchForm\" :model=\"searchForm\" :inline=\"true\" @submit.native.prevent>\r\n          <FormItem prop=\"date\">\r\n            <DatePicker type=\"daterange\" v-model=\"date\" placement=\"bottom-start\" @on-change=\"dateChange\"\r\n                        placeholder=\"申请开始日期-申请结束日期\" style=\"width: 200px\"></DatePicker>\r\n          </FormItem>\r\n          <FormItem prop=\"shop\">\r\n            <ShopSelect v-model=\"searchForm.shops\" placeholder=\"选择店铺\" width=\"205px\" valueField=\"id\" :isOverseas=\"true\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"sellers\" class=\"sellerSelectItem\">\r\n            <Select multiple type=\"text\" v-model=\"searchForm.sellers\" placeholder=\"销售员\" filterable :max-tag-count=\"1\" style=\"width: 233px\" :transfer=\"true\" >\r\n              <Option v-for=\"item in sellerArr\" :value=\"item.id\" :key=\"item.id\">{{ item.nickName }}</Option>\r\n            </Select>\r\n            <Button type=\"dashed\" @click=\"openPerson\" style=\"margin-left: 3px\" size=\"default\">选择</Button>\r\n            <person-select :visible=\"personVisible\" :onCancel=\"()=>personVisible=false\"\r\n                           @setPerson=\"arr => (searchForm.sellers = arr.map(v => v.id))\" @setSelectInfo=\"setSelectInfo\"\r\n                           ref=\"personSelectRef\" groupName=\"operations_persons\" :multiple=\"true\" :isQuery=\"true\" />\r\n          </FormItem>\r\n          <FormItem prop=\"spu\">\r\n            <SpuSelect v-model=\"searchForm.spus\" placeholder=\"选择型号\" width=\"205px\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"sellerSku\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入销售SKU(回车分隔)\" @changeValue=\"(values)=>{ multiValuesSellerSku = values || []; }\" ref=\"multipleRefSellerSkuRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleSellerSku=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleSellerSku\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentSellerSku\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownSellerSku\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"productCode\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品编码(回车分隔)\" @changeValue=\"(values)=>{ multiValuesProductCode = values || []; }\" ref=\"multipleRefProductCodeRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleProductCode=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleProductCode\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentProductCode\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownProductCode\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"status\" :clear=\"true\">\r\n            <Select type=\"text\" v-model=\"searchForm.status\" placeholder=\"状态\" style=\"width:160px\">\r\n              <Option v-for=\"(item,index) in statusList\" :value=\"item.key\" :key=\"index\">{{ item['value'] }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>&nbsp;\r\n            <Button @click=\"handleReset()\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </div>\r\n      <div style=\"margin-bottom: 10px;\" class=\"reportTable\">\r\n        <Button @click=\"addNewApply\" type=\"primary\"  :loading=\"loading\">上新申请</Button>\r\n        <div style=\"display:inline-block\">\r\n          <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                  :on-success=\"handleImportSuccess\"\r\n                  :format=\"['xls', 'xlsx']\"\r\n                  :show-upload-list=\"false\"\r\n                  :on-format-error=\"handleImportFormatError\"\r\n                  :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n            <Button  class=\"buttonMargin\" :loading=\"loading\">上传文件</Button>\r\n          </Upload></div>\r\n        <Button @click=\"downTemplate\" class=\"buttonMargin\" :loading=\"loading\">下载模板</Button>\r\n        <Button @click=\"noticeSellerId\" class=\"buttonMargin\" :loading=\"loading\">通知运营</Button>\r\n        <Button @click=\"refreshStatus\" class=\"buttonMargin\" :loading=\"loading\">检测生成</Button>\r\n        <Button @click=\"handleExport\" class=\"buttonMargin\" :loading=\"loading\">导出</Button>\r\n        <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n               :data=\"tableData\" :loading=\"loading\" @on-selection-change=\"onSelectChange\">\r\n          <template v-slot:status=\"{row}\">\r\n            <span v-for=\"(item, index) in statusList\" :key=\"index\" v-copytext=\"item['value']\"\r\n                        v-if=\"item['key'] === row['status']\">{{ item['value'] }}</span>\r\n          </template>\r\n          <template v-slot:isUpc=\"{row}\">\r\n            <span v-for=\"(item, index) in yesNoOps\" :key=\"index\" v-copytext=\"item['name']\"\r\n                  v-if=\"item['key'] === row['isUpc']\">{{ item['name'] }}</span>\r\n          </template>\r\n          <template v-slot:reviewStatus=\"{row}\">\r\n            <Tag :color=\"getReviewStatusColor(row.reviewFlag)\">\r\n              {{ getReviewStatusText(row.reviewFlag) }}\r\n            </Tag>\r\n          </template>\r\n          <template v-slot:review=\"{row}\">\r\n            <Button\r\n              v-if=\"row.runReviewStatus === 1\"\r\n              type=\"success\"\r\n              size=\"small\"\r\n              @click=\"handleReview(row)\"\r\n              :loading=\"row.reviewLoading\">\r\n              审核\r\n            </Button>\r\n          </template>\r\n          <template v-slot:detailList=\"{ row }\">\r\n            <div class=\"requestDetailTd\">\r\n              <Row>\r\n                <i-col span=\"7\">销售SKU</i-col>\r\n                <i-col span=\"3\">产品编码</i-col>\r\n                <i-col span=\"6\">产品规格</i-col>\r\n                <i-col span=\"4\">UPC码</i-col>\r\n                <i-col span=\"4\">是否刊登</i-col>\r\n              </Row>\r\n              <Row v-for=\"(item, index) in (row.detailList ? row.detailList.filter((_, i) => i < 3) : [])\" :key=\"index\">\r\n                <i-col span=\"7\" v-copytext=\"item['sellerSku']\" :title=\"item['sellerSku']\" style=\"padding: 3px 0;\">{{item['sellerSku']}}</i-col>\r\n                <i-col span=\"3\" v-copytext=\"item['productCode']\" :title=\"item['productCode']\">{{ item['productCode'] }}</i-col>\r\n                <i-col span=\"6\" v-copytext=\"item['productSpec']\" :title=\"item['productSpec']\">{{ item['productSpec'] }}</i-col>\r\n                <i-col span=\"4\" v-copytext=\"item['upcCode']\" :title=\"item['upcCode']\">{{ item['upcCode'] }}</i-col>\r\n                <i-col span=\"4\" v-copytext=\"item['status']===1?'已刊登':'未刊登'\">{{ item['status']===1?'已刊登':'未刊登' }}</i-col>\r\n              </Row>\r\n              <div style=\"text-align: right; padding-top: 5px; padding-right: 5px\"\r\n                   v-if=\"row.detailList && row.detailList.length > 0\">\r\n                <Poptip\r\n                  title=\"明细信息\"\r\n                  content=\"content\"\r\n                  placement=\"left-end\"\r\n                  trigger=\"click\"\r\n                  :transfer=\"true\"\r\n                  :max-width=\"550\">\r\n                  <a @click=\"clickMore(row)\">查看更多</a>\r\n                  <div slot=\"content\" style=\"padding-bottom: 8px; max-height: 500px\">\r\n                    <Table\r\n                      :columns=\"popColumns\"\r\n                      :border=\"true\"\r\n                      :data=\"row.detailList || []\"\r\n                      size=\"small\"\r\n                      :max-height=\"420\"/>\r\n                  </div>\r\n                </Poptip>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <template v-slot:action=\"{ row }\">\r\n            <div>\r\n              <template v-for=\"(item, index) in getFilteredButtons(row).length > 3? getFilteredButtons(row).slice(0, 2): getFilteredButtons(row)\">\r\n                <a v-if=\"!isButtonDisabled(item, row)\"\r\n                   @click=\"item.function(row)\"\r\n                   style=\"margin-right: 10px\"\r\n                   :title=\"item.title\"\r\n                   :key=\"'btn-' + index\">{{item.text }}</a>\r\n                <span v-else\r\n                      style=\"margin-right: 10px; color: #c5c8ce; cursor: not-allowed;\"\r\n                      :title=\"item.title + '(已禁用)'\"\r\n                      :key=\"'disabled-btn-' + index\">{{item.text }}</span>\r\n              </template>\r\n              <Dropdown :transfer=\"true\" ref=\"dropdownRef\" @on-click=\"handleDropDownClick($event, row)\" transfer-class-name=\"inventory-manage-down-btns\" v-if=\"getFilteredButtons(row).length > 3\">\r\n                <a href=\"javascript:void(0)\"><span>更多</span><Icon type=\"ios-arrow-down\"></Icon></a>\r\n                <DropdownMenu slot=\"list\">\r\n                  <DropdownItem v-for=\"(item, index) in getFilteredButtons(row).slice(2)\"\r\n                                :name=\"item.type\"\r\n                                :key=\"index\"\r\n                                :disabled=\"isButtonDisabled(item, row)\">{{item.text}}</DropdownItem>\r\n                </DropdownMenu>\r\n              </Dropdown>\r\n            </div>\r\n          </template>\r\n        </Table>\r\n        <Page :total=\"searchForm.total\" size=\"small\" :current=\"searchForm.page\" :page-size=\"searchForm.limit\" :show-elevator=\"true\"\r\n              :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n      </div>\r\n      <NewApplyEdit ref=\"newApplyEditRef\" :visible=\"editVisible\" :title=\"editTitle\" :onCancel=\"(value)=>{this.editVisible=false;if(value){this.handleSearch()}}\"></NewApplyEdit>\r\n\r\n      <!-- 审核弹窗 -->\r\n      <Modal\r\n        v-model=\"reviewModalVisible\"\r\n        title=\"审核申请\"\r\n        :mask-closable=\"false\"\r\n        :closable=\"false\"\r\n        width=\"500\">\r\n        <Form ref=\"reviewForm\" :model=\"reviewForm\" :rules=\"reviewFormRules\" :label-width=\"80\">\r\n          <FormItem label=\"型号\">\r\n            <span>{{ reviewForm.spu }}</span>\r\n          </FormItem>\r\n          <FormItem label=\"审核结果\" prop=\"agree\">\r\n            <RadioGroup v-model=\"reviewForm.agree\">\r\n              <Radio label=\"true\">通过</Radio>\r\n              <Radio label=\"false\">驳回</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"审核意见\" prop=\"comment\">\r\n            <Input\r\n              v-model=\"reviewForm.comment\"\r\n              type=\"textarea\"\r\n              :autosize=\"{minRows: 3, maxRows: 6}\"\r\n              placeholder=\"请输入审核意见\"\r\n              maxlength=\"200\"\r\n              show-word-limit />\r\n          </FormItem>\r\n        </Form>\r\n        <div slot=\"footer\">\r\n          <Button @click=\"handleReviewCancel\">取消</Button>\r\n          <Button type=\"primary\" @click=\"handleReviewSubmit\" :loading=\"reviewLoading\">确定</Button>\r\n        </div>\r\n      </Modal>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {autoTableHeight, isEmpty} from \"@/libs/tools.js\";\r\nimport ShopSelect from \"@/components/shopSelect/index.vue\";\r\nimport SpuSelect from \"@/components/spu/index.vue\";\r\nimport Multiple from \"@/view/module/common/multipleInput.vue\";\r\nimport personSelect from \"_c/person-select-radio/index.vue\";\r\nimport NewApply from \"@/api/newApply/newApply\";\r\nimport Workflow from \"@/api/base/workflow\";\r\nimport NewApplyEdit from \"./edit.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\nexport default {\r\n  name: \"newApply\",\r\n  components: {personSelect, Multiple, ShopSelect,SpuSelect,NewApplyEdit},\r\n  data() {\r\n    const buttons = [{type: \"look\", text: \"查看\", title: \"点击查看\", function: this.lookData},\r\n                    {type: \"edit\", text: \"修改\", title: \"点击修改\", function: this.editData},\r\n                    {type: \"repeal\", text: \"作废\", title: \"点击作废\", function: this.delData},\r\n                    {type: \"remove\", text: \"删除\", title: \"点击删除\", function: this.repealData},]\r\n    return {\r\n      autoTableHeight,\r\n      buttons,\r\n      loading: false,\r\n      sellerArr:[],\r\n      currentRow:null,\r\n      personVisible:false,\r\n      yesNoOps:[{ key:1, name: \"是\" }, { key:0, name: \"否\" }],\r\n      multiValuesSellerSku:[],\r\n      popVisibleSellerSku:false,\r\n      popContentSellerSku: undefined,\r\n      multiValuesProductCode:[],\r\n      popVisibleProductCode:false,\r\n      popContentProductCode: undefined,\r\n      importURl: getUrl() + NewApply.path+ '/importFile',\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      selectData:[],\r\n      statusList:[{\"key\":-1,\"value\":\"全部\"},{\"key\":0,\"value\":\"已申请\"},{\"key\":1,\"value\":\"已刊登\"},{\"key\":2,\"value\":\"已作废\"},{\"key\":3,\"value\":\"部分刊登\"}],\r\n      date:[],\r\n      searchForm: {\r\n        startDate:null,\r\n        endDate:null,\r\n        shops: [],\r\n        spus:[],\r\n        status:null,\r\n        page:1,\r\n        limit:10\r\n      },\r\n      tableData: [], //表格数据\r\n      columns: [{type: 'selection',width: 55,},\r\n        {title: '申请日期',key: 'sheetDate',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['sheetDate']}>{row['sheetDate']}</span>)},\r\n        {title: '店铺',key: 'shopName',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['shopName']}>{row['shopName']}</span>)},\r\n        {title: '型号',key: 'spu',minWidth: 150,resizable:true,render: (_, { row }) => (<span v-copytext={row['spu']}>{row['spu']}</span>)},\r\n        {title: '销售人员',key: 'sellerName',minWidth: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['sellerName']}>{row['sellerName']}</span>)},\r\n        {title: '是否需要UPC',key: 'isUpc',minWidth: 120,resizable:true,slot:'isUpc'},\r\n        {title: '审核阶段',key: 'review',minWidth: 100,resizable:true,slot:'review'},\r\n        {title: '运输模式',key: 'fulfillmentType',minWidth: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['fulfillmentType']}>{row['fulfillmentType']}</span>)},\r\n        {title: 'Listing简称',key: 'listingTitle',minWidth: 200,resizable:true,render: (_, { row }) => (<span v-copytext={row['listingTitle']}>{row['listingTitle']}</span>)},\r\n        {title: 'sku定位',key: 'skuLoc',minWidth: 200,resizable:true,render: (_, { row }) => (<span v-copytext={row['skuLoc']}>{row['skuLoc']}</span>)},\r\n        {title: \"明细\", align: \"center\", minWidth: 400, key: \"detailList\",slot: 'detailList', className: \"requestDetailColumn\"},\r\n        {title: '使用状态',key: 'status',minWidth: 100,resizable:true,slot:'status'},\r\n        {title: '审批状态',key: 'reviewFlag',minWidth: 100,resizable:true,slot:'reviewStatus'},\r\n        {title: '创建人',key: 'createUserName',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['createUserName']}>{row['createUserName']}</span>)},\r\n        {title: '创建时间',key: 'createTime',width: 160,resizable:true,render:(_, { row }) => (<span v-copytext={row['createTime']}>{row['createTime']}</span>)},\r\n        {title: '操作',key: 'operate',width: 200,resizable:true,slot:\"action\"},\r\n      ],\r\n      popColumns: [\r\n        {title: \"销售SKU\", align: \"center\", minWidth: 300, key: \"sellerSku\",},\r\n        {title: \"产品编码\", align: \"center\", minWidth: 150, key: \"productCode\",},\r\n        {title: \"产品规格\", align: \"center\", minWidth: 300, key: \"productSpec\",},\r\n        {title: \"UPC码\", align: \"center\", minWidth: 150, key: \"upcCode\",},\r\n        {\r\n          title: \"是否刊登\",\r\n          align: \"center\",\r\n          minWidth: 150,\r\n          key: \"status\",\r\n          resizable: true,\r\n          render: (_, { row }) => {\r\n            const statusText = row.status === 1 ? '已刊登' : '未刊登';\r\n            return <span v-copytext={statusText}>{statusText}</span>;\r\n          }\r\n        }\r\n      ],\r\n      editVisible:false,\r\n      editTitle:\"上新管理-新增\",\r\n      // 审核弹窗相关\r\n      reviewModalVisible: false,\r\n      reviewLoading: false,\r\n      reviewForm: {\r\n        id: null,\r\n        spu: '',\r\n        agree: 'true', // 使用字符串，对应Radio的label\r\n        comment: ''\r\n      },\r\n      reviewFormRules: {\r\n        comment: [\r\n          { required: true, message: '请输入审核意见', trigger: 'blur' },\r\n          { min: 1, max: 200, message: '审核意见长度在1到200个字符', trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  //组件初始化进行的操作\r\n  mounted() {\r\n    this.handleSearch();\r\n  },\r\n  methods: {\r\n    handleImportError (err, file) {\r\n      this.loading=false;\r\n      this.$Message.error(file.message);\r\n    },\r\n    handleImportSuccess (res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res.code === 0) {\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.error(res.message);\r\n      }\r\n    },\r\n    handleMaxSize () {\r\n      this.$Message.warning('大小不能超过10M.');\r\n    },\r\n    handleImportFormatError (file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    openPerson(){\r\n      this.resetMultiple();\r\n      //打开人员选择\r\n      const { personSelectRef } = this.$refs;\r\n      const { sellerArr, searchForm } = this;\r\n      const selectedIds = searchForm.sellers || [];\r\n      if (personSelectRef) personSelectRef.setDefault(\r\n        sellerArr.filter(v => selectedIds.includes(v.id)).map(v=>({ name: v.nickName, id: v.id }))\r\n      );//给组件设置默认选中\r\n      this.personVisible = true;\r\n    },\r\n    // 人员选择相关\r\n    setSelectInfo(info={}){\r\n      this.sellerArr = info.personArr || [];\r\n    },\r\n    clickMore(row = {}) {\r\n      this.currentRow = {...row};\r\n    },\r\n    //操作\r\n    handleDropDownClick(name, row = {}) {\r\n      this.clickRow = { ...row };\r\n      // 检查按钮是否被禁用\r\n      const button = this.buttons.find(btn => btn.type === name);\r\n      if (button && this.isButtonDisabled(button, row)) {\r\n        this.$Message.warning('当前状态下该操作不可用');\r\n        return;\r\n      }\r\n\r\n      switch (name) {\r\n        case \"look\":\r\n          this.lookData(row);\r\n          break;\r\n        case \"edit\":\r\n          this.editData(row);\r\n          break;\r\n        case \"remove\":\r\n          this.delData(row);\r\n          break;\r\n        case \"repeal\":\r\n          this.repealData(row);\r\n          break;\r\n        default:\r\n      }\r\n    },\r\n    // 获取过滤后的按钮列表\r\n    getFilteredButtons(row) {\r\n      return this.buttons;\r\n    },\r\n    // 判断按钮是否应该被禁用\r\n    isButtonDisabled(button, row) {\r\n      // 当reviewFlag为1时，禁用修改、作废、删除按钮\r\n      if (row.reviewFlag === 1 && row.status !== 5) {\r\n        // , 'remove'\r\n        return ['edit', 'repeal'].includes(button.type);\r\n      }\r\n      return false;\r\n    },\r\n    // 获取审批状态文本\r\n    getReviewStatusText(reviewFlag) {\r\n      switch (reviewFlag) {\r\n        case 0:\r\n          return '无需审批';\r\n        case 1:\r\n          return '需要审';\r\n        default:\r\n          return '未知状态';\r\n      }\r\n    },\r\n    // 获取审批状态颜色\r\n    getReviewStatusColor(reviewFlag) {\r\n      switch (reviewFlag) {\r\n        case 0:\r\n          return 'default';\r\n        case 1:\r\n          return 'orange';\r\n        default:\r\n          return 'default';\r\n      }\r\n    },\r\n    editData(row){\r\n      // 检查是否被禁用\r\n      if (row.reviewFlag === 1) {\r\n        this.$Message.warning('当前记录正在审批流程中，无法修改');\r\n        return;\r\n      }\r\n      this.editVisible = true;\r\n      const { newApplyEditRef } = this.$refs;\r\n      if (newApplyEditRef) newApplyEditRef.setDefault(row,'Edit');//给组件\r\n    },\r\n    lookData(row){\r\n      this.editVisible = true;\r\n      const { newApplyEditRef } = this.$refs;\r\n      if (newApplyEditRef) newApplyEditRef.setDefault(row,'View');//给组件\r\n    },\r\n    delData(row){\r\n      // 检查是否被禁用\r\n      // if (row.reviewFlag === 1) {\r\n      //   this.$Message.warning('当前记录正在审批流程中，无法删除');\r\n      //   return;\r\n      // }\r\n      this.loading=true;\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这些数据吗？',\r\n        onOk: () => {\r\n          NewApply.remove({\"ids\":row.id}).then(res=>{\r\n            if(res && res['code'] === 0){\r\n              this.handleSearch();\r\n            }else{\r\n              this.$message.error(res['message']);\r\n            }\r\n          }).finally(()=>{this.loading=false;})\r\n        },\r\n      })\r\n    },\r\n    repealData(row){\r\n      // 检查是否被禁用\r\n      if (row.reviewFlag === 1) {\r\n        this.$Message.warning('当前记录正在审批流程中，无法作废');\r\n        return;\r\n      }\r\n      this.loading=true;\r\n      NewApply.repeal({\"ids\":row.id}).then(res=>{\r\n        if(res && res['code'] === 0){\r\n          this.handleSearch();\r\n        }else{\r\n          this.$message.error(res['message']);\r\n        }\r\n      }).finally(()=>{this.loading=false;})\r\n    },\r\n    closeDropdownSellerSku() { //关闭输入文本框\r\n      const { popContentSellerSku } = this;\r\n      const { multipleRefSellerSkuRef } = this.$refs;\r\n      this.popVisibleSellerSku = false;\r\n      if(!popContentSellerSku) return;\r\n      const content = popContentSellerSku ? popContentSellerSku.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesSellerSku = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesSellerSku = [...new Set(this.multiValuesSellerSku)];\r\n      if(multipleRefSellerSkuRef && multipleRefSellerSkuRef.setValueArray){\r\n        multipleRefSellerSkuRef.setValueArray(this.multiValuesSellerSku);\r\n      }\r\n    },\r\n    closeDropdownProductCode() { //关闭输入文本框\r\n      const { popContentProductCode } = this;\r\n      const { multipleRefProductCodeRef } = this.$refs;\r\n      this.popVisibleProductCode = false;\r\n      if(!popContentProductCode) return;\r\n      const content = popContentProductCode ? popContentProductCode.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesProductCode = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesProductCode = [...new Set(this.multiValuesProductCode)];\r\n      if(multipleRefProductCodeRef && multipleRefProductCodeRef.setValueArray){\r\n        multipleRefProductCodeRef.setValueArray(this.multiValuesProductCode);\r\n      }\r\n    },\r\n    getParam(){\r\n      const params = {\r\n        ...this.searchForm\r\n      };\r\n      delete params.shops;\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\"&#&\") : undefined;\r\n      if (this.multiValuesSellerSku.length > 0){\r\n        params[\"sellerSkus\"] = getStr(this.multiValuesSellerSku);\r\n      }\r\n      if (this.multiValuesProductCode.length > 0){\r\n        params[\"productCodes\"] = getStr(this.multiValuesProductCode);\r\n      }\r\n      params[\"sellerIds\"] = getStr(this.searchForm.sellers);\r\n      params[\"shopIds\"] = getStr(this.searchForm.shops);\r\n      params[\"spus\"] = getStr(this.searchForm.spus);\r\n      return params;\r\n    },\r\n    dateChange(date) {\r\n      if (isEmpty(date)) {\r\n        this.searchForm.startDate = '';\r\n        this.searchForm.endDate = '';\r\n      } else {\r\n        this.searchForm.startDate = date[0];\r\n        this.searchForm.endDate = date[1];\r\n      }\r\n    },\r\n    //查询\r\n    handleSearch() {\r\n      const params = this.getParam();\r\n      this.loading = true\r\n      NewApply.listPage(params).then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.tableData = res.data.records;\r\n          this.searchForm.total = parseInt(res.data.total);\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    handleReset() {\r\n      //重置验证\r\n      this.$refs['searchFormRef'].resetFields();\r\n      this.searchForm.shops=[];\r\n      this.searchForm.spus=[];\r\n      this.searchForm.startDate=null;\r\n      this.searchForm.endDate=null;\r\n      this.date = [];\r\n      this.resetMultiple(true);\r\n    },\r\n    resetMultiple(clearTxt = false) {\r\n      if (clearTxt === true) {\r\n        this.multiValuesSellerSku = [];\r\n        const { multipleRefSellerSkuRef } = this.$refs;\r\n        if (multipleRefSellerSkuRef && multipleRefSellerSkuRef.setValueArray) {\r\n          multipleRefSellerSkuRef.setValueArray([]);\r\n        }\r\n        this.multiValuesProductCode = [];\r\n        const { multipleRefProductCodeRef } = this.$refs;\r\n        if (multipleRefProductCodeRef && multipleRefProductCodeRef.setValueArray) {\r\n          multipleRefProductCodeRef.setValueArray([]);\r\n        }\r\n      }\r\n      this.popContentSellerSku = undefined;\r\n      this.popVisibleSellerSku = false;\r\n      this.popContentProductCode = undefined;\r\n      this.popVisibleProductCode = false;\r\n    },\r\n    handlePage(current) {\r\n      this.searchForm.page = current\r\n      this.handleSearch()\r\n    },\r\n    handlePageSize(size) {\r\n      this.searchForm.limit = size\r\n      this.handleSearch()\r\n    },\r\n    onSelectChange(selection){\r\n      this.selectData = selection;\r\n    },\r\n    downTemplate() {\r\n      this.loading = true;\r\n      let params = {};\r\n      params['fileName'] = \"上新申请导入模板.xls\";\r\n      NewApply.downTemplate(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    handleDel(){\r\n      let ids = this.selectData.map(item => item['id']).join(',');\r\n      if (!ids) {\r\n        this.$message.error(\"请选择需要作废的记录\");\r\n        return;\r\n      }\r\n      let sellerSkuUsed = this.selectData.filter(item => item['status'] >= 2).map(item=>item['sellerSku']).join(',');\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这些数据吗？'+(!!sellerSkuUsed?(\"并且存在已匹配的销售SKU\"+sellerSkuUsed):\"\"),\r\n        onOk: () => {\r\n          this.loading = true;\r\n          NewApply.remove({ids: ids}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.$Message.success('删除成功!');\r\n              this.handleSearch();\r\n            } else {\r\n              this.$Message.error(res['message']);\r\n            }\r\n          }).finally(()=>this.loading=false)\r\n        },\r\n      })\r\n    },\r\n    handleExport(){\r\n      this.loading = true;\r\n      let params = this.getParam();\r\n      params['fileName'] = \"上新申请\" + new Date().getTime() + \".xls\";\r\n      NewApply.exportFile(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    addNewApply(){\r\n      this.editVisible=true;\r\n      const { newApplyEditRef } = this.$refs;\r\n      if (newApplyEditRef) newApplyEditRef.setDefault(null,'Add');//给组件\r\n    },\r\n    noticeSellerId(){\r\n      let ids = this.selectData.map(item => item['id']).join(',');\r\n      if (!ids) {\r\n        this.$message.error(\"请选择需要通知运营的记录\");\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      NewApply.noticeSeller({ids: ids}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.$Message.success('通知成功!');\r\n        } else {\r\n          this.$Message.error(res['message']);\r\n        }\r\n      }).finally(()=>this.loading=false)\r\n    },\r\n    refreshStatus(){\r\n      let ids = this.selectData.map(item => item['id']).join(',');\r\n      this.loading = true;\r\n      NewApply.refreshStatus({ids: ids}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.$Message.success('运行成功!');\r\n        } else {\r\n          this.$Message.error(res['message']);\r\n        }\r\n      }).finally(()=>this.loading=false)\r\n    },\r\n    // 审批申请处理\r\n    handleApply(row) {\r\n      this.$Modal.confirm({\r\n        title: '确认申请',\r\n        content: `确定要为型号 \"${row.spu}\" 申请审批吗？`,\r\n        onOk: () => {\r\n          this.$set(row, 'reviewLoading', true);\r\n          // 这里调用审批申请的API\r\n          NewApply.apply({ id: row.id }).then(res => {\r\n            if (res && res.code === 0) {\r\n              this.$Message.success('审批申请提交成功！');\r\n              this.handleSearch(); // 刷新列表\r\n            } else {\r\n              this.$Message.error(res.message || '审批申请失败');\r\n            }\r\n          }).catch(err => {\r\n            this.$Message.error('审批申请失败：' + (err.message || '网络错误'));\r\n          }).finally(() => {\r\n            this.$set(row, 'reviewLoading', false);\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 审批审核处理\r\n    handleReview(row) {\r\n      // 显示审核弹窗\r\n      this.showReviewModal(row);\r\n    },\r\n\r\n    showReviewModal(row) {\r\n      this.reviewForm = {\r\n        id: row.id,\r\n        spu: row.spu,\r\n        agree: 'true', // 默认选择通过，使用字符串\r\n        comment: '' // 审核意见\r\n      };\r\n      this.reviewModalVisible = true;\r\n    },\r\n\r\n    handleReviewSubmit() {\r\n      this.$refs.reviewForm.validate((valid) => {\r\n        if (valid) {\r\n          this.reviewLoading = true;\r\n          const params = {\r\n            id: this.reviewForm.id,\r\n            variables: {\r\n              agree: this.reviewForm.agree === 'true', // 转换为布尔值\r\n              comment: this.reviewForm.comment\r\n            }\r\n          };\r\n\r\n          Workflow.review(params).then(res => {\r\n            if (res && res.code === 0) {\r\n              this.$Message.success('审批审核完成！');\r\n              this.reviewModalVisible = false;\r\n              this.handleSearch(); // 刷新列表\r\n            } else {\r\n              this.$Message.error(res.message || '审批审核失败');\r\n            }\r\n          }).catch(err => {\r\n            this.$Message.error('审批审核失败：' + (err.message || '网络错误'));\r\n          }).finally(() => {\r\n            this.reviewLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    handleReviewCancel() {\r\n      this.reviewModalVisible = false;\r\n      this.reviewForm = {\r\n        id: null,\r\n        spu: '',\r\n        agree: 'true', // 使用字符串\r\n        comment: ''\r\n      };\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.search-con-top{\r\n  position: relative;\r\n  padding: 0;\r\n  //标签、sku、asin搜索项\r\n  .multiClass{\r\n    .flex-h{\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-top:2px;\r\n    }\r\n  }\r\n}\r\n.widthClass {\r\n  width: 350px\r\n}\r\n.buttonMargin{\r\n  margin-left:15px;\r\n}\r\n.salesRank {\r\n  .reportTable {\r\n    .requestDetailColumn {\r\n      .ivu-table-cell {\r\n        padding-left: 2px;\r\n        padding-right: 2px;\r\n\r\n        .requestDetailTd {\r\n          padding: 0 0;\r\n\r\n          .ivu-row {\r\n            border-bottom: 1px solid #e8eaec;\r\n\r\n            &:last-child {\r\n              border-bottom: none;\r\n            }\r\n\r\n            .ivu-col {\r\n              border-right: 1px solid #e8eaec;\r\n              padding: 3px 2px;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n\r\n              &:last-child {\r\n                border-right: none;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}