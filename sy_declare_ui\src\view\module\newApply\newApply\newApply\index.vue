<!--
@create date 2025-02-07
@desc 上新管理
-->
<template>
  <div class="search-con-top salesRank">
    <Card :shadow="true">
      <div>
        <Form ref="searchFormRef" class="searchForm" :model="searchForm" :inline="true" @submit.native.prevent>
          <FormItem prop="date">
            <DatePicker type="daterange" v-model="date" placement="bottom-start" @on-change="dateChange"
                        placeholder="申请开始日期-申请结束日期" style="width: 200px"></DatePicker>
          </FormItem>
          <FormItem prop="shop">
            <ShopSelect v-model="searchForm.shops" placeholder="选择店铺" width="205px" valueField="id" :isOverseas="true"/>
          </FormItem>
          <FormItem prop="sellers" class="sellerSelectItem">
            <Select multiple type="text" v-model="searchForm.sellers" placeholder="销售员" filterable :max-tag-count="1" style="width: 233px" :transfer="true" >
              <Option v-for="item in sellerArr" :value="item.id" :key="item.id">{{ item.nickName }}</Option>
            </Select>
            <Button type="dashed" @click="openPerson" style="margin-left: 3px" size="default">选择</Button>
            <person-select :visible="personVisible" :onCancel="()=>personVisible=false"
                           @setPerson="arr => (searchForm.sellers = arr.map(v => v.id))" @setSelectInfo="setSelectInfo"
                           ref="personSelectRef" groupName="operations_persons" :multiple="true" :isQuery="true" />
          </FormItem>
          <FormItem prop="spu">
            <SpuSelect v-model="searchForm.spus" placeholder="选择型号" width="205px"/>
          </FormItem>
          <FormItem prop="sellerSku" class="multiClass">
            <div class="flex-h">
              <Multiple placeholder="请输入销售SKU(回车分隔)" @changeValue="(values)=>{ multiValuesSellerSku = values || []; }" ref="multipleRefSellerSkuRef" style="height:32px;"></Multiple>
              <Button :visible="false" @click="()=>{popVisibleSellerSku=true;}">输入</Button>
              <Dropdown trigger="custom" :visible="popVisibleSellerSku" style="margin-left: 3px" :transfer="true" transfer-class-name="orderBillDrop">
                <template #list>
                  <DropdownMenu class="popContentClass">
                    <Input v-model="popContentSellerSku" type="textarea" :autosize="{minRows: 4,maxRows: 8}" placeholder="请输入内容，回车或逗号分隔" style="width: 260px"/>
                    <div style="text-align: right; padding-top: 3px">
                      <Button type="info" size="small" @click="closeDropdownSellerSku">确定</Button>
                    </div>
                  </DropdownMenu>
                </template>
              </Dropdown>
            </div>
          </FormItem>
          <FormItem prop="productCode" class="multiClass">
            <div class="flex-h">
              <Multiple placeholder="请输入产品编码(回车分隔)" @changeValue="(values)=>{ multiValuesProductCode = values || []; }" ref="multipleRefProductCodeRef" style="height:32px;"></Multiple>
              <Button :visible="false" @click="()=>{popVisibleProductCode=true;}">输入</Button>
              <Dropdown trigger="custom" :visible="popVisibleProductCode" style="margin-left: 3px" :transfer="true" transfer-class-name="orderBillDrop">
                <template #list>
                  <DropdownMenu class="popContentClass">
                    <Input v-model="popContentProductCode" type="textarea" :autosize="{minRows: 4,maxRows: 8}" placeholder="请输入内容，回车或逗号分隔" style="width: 260px"/>
                    <div style="text-align: right; padding-top: 3px">
                      <Button type="info" size="small" @click="closeDropdownProductCode">确定</Button>
                    </div>
                  </DropdownMenu>
                </template>
              </Dropdown>
            </div>
          </FormItem>
          <FormItem prop="status" :clear="true">
            <Select type="text" v-model="searchForm.status" placeholder="状态" style="width:160px">
              <Option v-for="(item,index) in statusList" :value="item.key" :key="index">{{ item['value'] }}</Option>
            </Select>
          </FormItem>
          <FormItem>
            <Button type="primary" @click="handleSearch()">查询</Button>&nbsp;
            <Button @click="handleReset()">重置</Button>
          </FormItem>
        </Form>
      </div>
      <div style="margin-bottom: 10px;" class="reportTable">
        <Button @click="addNewApply" type="primary"  :loading="loading">上新申请</Button>
        <div style="display:inline-block">
          <Upload ref="uploadFileRef" name="importFile" :action="importURl" :max-size="10240"
                  :on-success="handleImportSuccess"
                  :format="['xls', 'xlsx']"
                  :show-upload-list="false"
                  :on-format-error="handleImportFormatError"
                  :on-error="handleImportError" :headers="loginInfo" :on-exceeded-size="handleMaxSize">
            <Button  class="buttonMargin" :loading="loading">上传文件</Button>
          </Upload></div>
        <Button @click="downTemplate" class="buttonMargin" :loading="loading">下载模板</Button>
        <Button @click="noticeSellerId" class="buttonMargin" :loading="loading">通知运营</Button>
        <Button @click="refreshStatus" class="buttonMargin" :loading="loading">检测生成</Button>
        <Button @click="handleExport" class="buttonMargin" :loading="loading">导出</Button>
        <Table :border="true" ref="autoTableRef" :max-height="autoTableHeight($refs.autoTableRef)" :columns="columns"
               :data="tableData" :loading="loading" @on-selection-change="onSelectChange">
          <template v-slot:status="{row}">
            <span v-for="(item, index) in statusList" :key="index" v-copytext="item['value']"
                        v-if="item['key'] === row['status']">{{ item['value'] }}</span>
          </template>
          <template v-slot:isUpc="{row}">
            <span v-for="(item, index) in yesNoOps" :key="index" v-copytext="item['name']"
                  v-if="item['key'] === row['isUpc']">{{ item['name'] }}</span>
          </template>
          <template v-slot:reviewStatus="{row}">
            <Tag :color="getReviewStatusColor(row.reviewFlag, row.status)">
              {{ getReviewStatusText(row.reviewFlag, row.status) }}
            </Tag>
          </template>
          <template v-slot:review="{row}">
            <Button
              v-if="row.runReviewStatus === 1"
              type="success"
              size="small"
              @click="handleReview(row)"
              :loading="row.reviewLoading">
              审核
            </Button>
          </template>
          <template v-slot:detailList="{ row }">
            <div class="requestDetailTd">
              <Row>
                <i-col span="7">销售SKU</i-col>
                <i-col span="3">产品编码</i-col>
                <i-col span="6">产品规格</i-col>
                <i-col span="4">UPC码</i-col>
                <i-col span="4">是否刊登</i-col>
              </Row>
              <Row v-for="(item, index) in (row.detailList ? row.detailList.filter((_, i) => i < 3) : [])" :key="index">
                <i-col span="7" v-copytext="item['sellerSku']" :title="item['sellerSku']" style="padding: 3px 0;">{{item['sellerSku']}}</i-col>
                <i-col span="3" v-copytext="item['productCode']" :title="item['productCode']">{{ item['productCode'] }}</i-col>
                <i-col span="6" v-copytext="item['productSpec']" :title="item['productSpec']">{{ item['productSpec'] }}</i-col>
                <i-col span="4" v-copytext="item['upcCode']" :title="item['upcCode']">{{ item['upcCode'] }}</i-col>
                <i-col span="4" v-copytext="item['status']===1?'已刊登':'未刊登'">{{ item['status']===1?'已刊登':'未刊登' }}</i-col>
              </Row>
              <div style="text-align: right; padding-top: 5px; padding-right: 5px"
                   v-if="row.detailList && row.detailList.length > 0">
                <Poptip
                  title="明细信息"
                  content="content"
                  placement="left-end"
                  trigger="click"
                  :transfer="true"
                  :max-width="550">
                  <a @click="clickMore(row)">查看更多</a>
                  <div slot="content" style="padding-bottom: 8px; max-height: 500px">
                    <Table
                      :columns="popColumns"
                      :border="true"
                      :data="row.detailList || []"
                      size="small"
                      :max-height="420"/>
                  </div>
                </Poptip>
              </div>
            </div>
          </template>
          <template v-slot:action="{ row }">
            <div>
              <template v-for="(item, index) in getFilteredButtons(row).length > 3? getFilteredButtons(row).slice(0, 2): getFilteredButtons(row)">
                <a v-if="!isButtonDisabled(item, row)"
                   @click="item.function(row)"
                   style="margin-right: 10px"
                   :title="item.title"
                   :key="'btn-' + index">{{item.text }}</a>
                <span v-else
                      style="margin-right: 10px; color: #c5c8ce; cursor: not-allowed;"
                      :title="item.title + '(已禁用)'"
                      :key="'disabled-btn-' + index">{{item.text }}</span>
              </template>
              <Dropdown :transfer="true" ref="dropdownRef" @on-click="handleDropDownClick($event, row)" transfer-class-name="inventory-manage-down-btns" v-if="getFilteredButtons(row).length > 3">
                <a href="javascript:void(0)"><span>更多</span><Icon type="ios-arrow-down"></Icon></a>
                <DropdownMenu slot="list">
                  <DropdownItem v-for="(item, index) in getFilteredButtons(row).slice(2)"
                                :name="item.type"
                                :key="index"
                                :disabled="isButtonDisabled(item, row)">{{item.text}}</DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </div>
          </template>
        </Table>
        <Page :total="searchForm.total" size="small" :current="searchForm.page" :page-size="searchForm.limit" :show-elevator="true"
              :show-sizer="true" :show-total="true" @on-change="handlePage" @on-page-size-change='handlePageSize'></Page>
      </div>
      <NewApplyEdit ref="newApplyEditRef" :visible="editVisible" :title="editTitle" :onCancel="(value)=>{this.editVisible=false;if(value){this.handleSearch()}}"></NewApplyEdit>

      <!-- 审核弹窗 -->
      <Modal
        v-model="reviewModalVisible"
        title="审核申请"
        :mask-closable="false"
        :closable="false"
        width="500">
        <Form ref="reviewForm" :model="reviewForm" :rules="reviewFormRules" :label-width="80">
          <FormItem label="型号">
            <span>{{ reviewForm.spu }}</span>
          </FormItem>
          <FormItem label="审核结果" prop="agree">
            <RadioGroup v-model="reviewForm.agree">
              <Radio label="true">通过</Radio>
              <Radio label="false">驳回</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="审核意见" prop="comment">
            <Input
              v-model="reviewForm.comment"
              type="textarea"
              :autosize="{minRows: 3, maxRows: 6}"
              placeholder="请输入审核意见"
              maxlength="200"
              show-word-limit />
          </FormItem>
        </Form>
        <div slot="footer">
          <Button @click="handleReviewCancel">取消</Button>
          <Button type="primary" @click="handleReviewSubmit" :loading="reviewLoading">确定</Button>
        </div>
      </Modal>
    </Card>
  </div>
</template>

<script>
import {autoTableHeight, isEmpty} from "@/libs/tools.js";
import ShopSelect from "@/components/shopSelect/index.vue";
import SpuSelect from "@/components/spu/index.vue";
import Multiple from "@/view/module/common/multipleInput.vue";
import personSelect from "_c/person-select-radio/index.vue";
import NewApply from "@/api/newApply/newApply";
import Workflow from "@/api/base/workflow";
import NewApplyEdit from "./edit.vue";
import {getToken, getUrl} from "@/libs/util";
export default {
  name: "newApply",
  components: {personSelect, Multiple, ShopSelect,SpuSelect,NewApplyEdit},
  data() {
    const buttons = [{type: "look", text: "查看", title: "点击查看", function: this.lookData},
                    {type: "edit", text: "修改", title: "点击修改", function: this.editData},
                    {type: "repeal", text: "作废", title: "点击作废", function: this.delData},
                    {type: "remove", text: "删除", title: "点击删除", function: this.repealData},]
    return {
      autoTableHeight,
      buttons,
      loading: false,
      sellerArr:[],
      currentRow:null,
      personVisible:false,
      yesNoOps:[{ key:1, name: "是" }, { key:0, name: "否" }],
      multiValuesSellerSku:[],
      popVisibleSellerSku:false,
      popContentSellerSku: undefined,
      multiValuesProductCode:[],
      popVisibleProductCode:false,
      popContentProductCode: undefined,
      importURl: getUrl() + NewApply.path+ '/importFile',
      loginInfo: {
        Accept: 'application/json,text/plain, */*, */*',
        mode: 'cors',
        Authorization: 'Bearer ' + getToken()
      },
      selectData:[],
      statusList:[{"key":-1,"value":"全部"},{"key":0,"value":"已申请"},{"key":1,"value":"已刊登"},{"key":2,"value":"已作废"},{"key":3,"value":"部分刊登"}],
      date:[],
      searchForm: {
        startDate:null,
        endDate:null,
        shops: [],
        spus:[],
        status:null,
        page:1,
        limit:10
      },
      tableData: [], //表格数据
      columns: [{type: 'selection',width: 55,},
        {title: '申请日期',key: 'sheetDate',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['sheetDate']}>{row['sheetDate']}</span>)},
        {title: '店铺',key: 'shopName',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['shopName']}>{row['shopName']}</span>)},
        {title: '型号',key: 'spu',minWidth: 150,resizable:true,render: (_, { row }) => (<span v-copytext={row['spu']}>{row['spu']}</span>)},
        {title: '销售人员',key: 'sellerName',minWidth: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['sellerName']}>{row['sellerName']}</span>)},
        {title: '是否需要UPC',key: 'isUpc',minWidth: 120,resizable:true,slot:'isUpc'},
        {title: '审核阶段',key: 'review',minWidth: 100,resizable:true,slot:'review'},
        {title: '运输模式',key: 'fulfillmentType',minWidth: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['fulfillmentType']}>{row['fulfillmentType']}</span>)},
        {title: 'Listing简称',key: 'listingTitle',minWidth: 200,resizable:true,render: (_, { row }) => (<span v-copytext={row['listingTitle']}>{row['listingTitle']}</span>)},
        {title: 'sku定位',key: 'skuLoc',minWidth: 200,resizable:true,render: (_, { row }) => (<span v-copytext={row['skuLoc']}>{row['skuLoc']}</span>)},
        {title: "明细", align: "center", minWidth: 400, key: "detailList",slot: 'detailList', className: "requestDetailColumn"},
        {title: '使用状态',key: 'status',minWidth: 100,resizable:true,slot:'status'},
        {title: '审批状态',key: 'reviewFlag',minWidth: 100,resizable:true,slot:'reviewStatus'},
        {title: '创建人',key: 'createUserName',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['createUserName']}>{row['createUserName']}</span>)},
        {title: '创建时间',key: 'createTime',width: 160,resizable:true,render:(_, { row }) => (<span v-copytext={row['createTime']}>{row['createTime']}</span>)},
        {title: '操作',key: 'operate',width: 200,resizable:true,slot:"action"},
      ],
      popColumns: [
        {title: "销售SKU", align: "center", minWidth: 300, key: "sellerSku",},
        {title: "产品编码", align: "center", minWidth: 150, key: "productCode",},
        {title: "产品规格", align: "center", minWidth: 300, key: "productSpec",},
        {title: "UPC码", align: "center", minWidth: 150, key: "upcCode",},
        {
          title: "是否刊登",
          align: "center",
          minWidth: 150,
          key: "status",
          resizable: true,
          render: (_, { row }) => {
            const statusText = row.status === 1 ? '已刊登' : '未刊登';
            return <span v-copytext={statusText}>{statusText}</span>;
          }
        }
      ],
      editVisible:false,
      editTitle:"上新管理-新增",
      // 审核弹窗相关
      reviewModalVisible: false,
      reviewLoading: false,
      reviewForm: {
        id: null,
        spu: '',
        agree: 'true', // 使用字符串，对应Radio的label
        comment: ''
      },
      reviewFormRules: {
        comment: [
          { required: true, message: '请输入审核意见', trigger: 'blur' },
          { min: 1, max: 200, message: '审核意见长度在1到200个字符', trigger: 'blur' }
        ]
      }
    };
  },
  //组件初始化进行的操作
  mounted() {
    this.handleSearch();
  },
  methods: {
    handleImportError (err, file) {
      this.loading=false;
      this.$Message.error(file.message);
    },
    handleImportSuccess (res) {
      this.$refs['uploadFileRef'].clearFiles();
      if (res.code === 0) {
        this.handleSearch();
      } else {
        this.$Message.error(res.message);
      }
    },
    handleMaxSize () {
      this.$Message.warning('大小不能超过10M.');
    },
    handleImportFormatError (file) {
      //格式验证失败的钩子
      this.$Modal.error({
        title: '文件格式不正确',
        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',
        okText: '确认'
      });
    },
    openPerson(){
      this.resetMultiple();
      //打开人员选择
      const { personSelectRef } = this.$refs;
      const { sellerArr, searchForm } = this;
      const selectedIds = searchForm.sellers || [];
      if (personSelectRef) personSelectRef.setDefault(
        sellerArr.filter(v => selectedIds.includes(v.id)).map(v=>({ name: v.nickName, id: v.id }))
      );//给组件设置默认选中
      this.personVisible = true;
    },
    // 人员选择相关
    setSelectInfo(info={}){
      this.sellerArr = info.personArr || [];
    },
    clickMore(row = {}) {
      this.currentRow = {...row};
    },
    //操作
    handleDropDownClick(name, row = {}) {
      this.clickRow = { ...row };
      // 检查按钮是否被禁用
      const button = this.buttons.find(btn => btn.type === name);
      if (button && this.isButtonDisabled(button, row)) {
        this.$Message.warning('当前状态下该操作不可用');
        return;
      }

      switch (name) {
        case "look":
          this.lookData(row);
          break;
        case "edit":
          this.editData(row);
          break;
        case "remove":
          this.delData(row);
          break;
        case "repeal":
          this.repealData(row);
          break;
        default:
      }
    },
    // 获取过滤后的按钮列表
    getFilteredButtons(row) {
      return this.buttons;
    },
    // 判断按钮是否应该被禁用
    isButtonDisabled(button, row) {
      // 当reviewFlag为1时，禁用修改、作废、删除按钮
      if (row.reviewFlag === 1 && row.status !== 5) {
        // , 'remove'
        return ['edit', 'repeal'].includes(button.type);
      }
      return false;
    },
    // 获取审批状态文本
    getReviewStatusText(reviewFlag, status) {
      switch (reviewFlag) {
        case 0:
          return '无需审批';
        case 1:
          if (status === 5) {
            return '已驳回';
          } else if (status === 6) {
            return '审核结束';
          }
          return '需要审';
        default:
          return '未知状态';
      }
    },
    // 获取审批状态颜色
    getReviewStatusColor(reviewFlag) {
      switch (reviewFlag) {
        case 0:
          return 'default';
        case 1:
          return 'orange';
        default:
          return 'default';
      }
    },
    editData(row){
      // 检查是否被禁用
      // if (row.reviewFlag === 1) {
      //   this.$Message.warning('当前记录正在审批流程中，无法修改');
      //   return;
      // }
      this.editVisible = true;
      const { newApplyEditRef } = this.$refs;
      if (newApplyEditRef) newApplyEditRef.setDefault(row,'Edit');//给组件
    },
    lookData(row){
      this.editVisible = true;
      const { newApplyEditRef } = this.$refs;
      if (newApplyEditRef) newApplyEditRef.setDefault(row,'View');//给组件
    },
    delData(row){
      // 检查是否被禁用
      // if (row.reviewFlag === 1) {
      //   this.$Message.warning('当前记录正在审批流程中，无法删除');
      //   return;
      // }
      this.loading=true;
      this.$Modal.confirm({
        title: '提示',
        content: '您确认要删除这些数据吗？',
        onOk: () => {
          NewApply.remove({"ids":row.id}).then(res=>{
            if(res && res['code'] === 0){
              this.handleSearch();
            }else{
              this.$message.error(res['message']);
            }
          }).finally(()=>{this.loading=false;})
        },
      })
    },
    repealData(row){
      // 检查是否被禁用
      if (row.reviewFlag === 1) {
        this.$Message.warning('当前记录正在审批流程中，无法作废');
        return;
      }
      this.loading=true;
      NewApply.repeal({"ids":row.id}).then(res=>{
        if(res && res['code'] === 0){
          this.handleSearch();
        }else{
          this.$message.error(res['message']);
        }
      }).finally(()=>{this.loading=false;})
    },
    closeDropdownSellerSku() { //关闭输入文本框
      const { popContentSellerSku } = this;
      const { multipleRefSellerSkuRef } = this.$refs;
      this.popVisibleSellerSku = false;
      if(!popContentSellerSku) return;
      const content = popContentSellerSku ? popContentSellerSku.trim().replace(/，/g, ",") : '';
      this.multiValuesSellerSku = content.split('\n').filter(v=>!!v);
      this.multiValuesSellerSku = [...new Set(this.multiValuesSellerSku)];
      if(multipleRefSellerSkuRef && multipleRefSellerSkuRef.setValueArray){
        multipleRefSellerSkuRef.setValueArray(this.multiValuesSellerSku);
      }
    },
    closeDropdownProductCode() { //关闭输入文本框
      const { popContentProductCode } = this;
      const { multipleRefProductCodeRef } = this.$refs;
      this.popVisibleProductCode = false;
      if(!popContentProductCode) return;
      const content = popContentProductCode ? popContentProductCode.trim().replace(/，/g, ",") : '';
      this.multiValuesProductCode = content.split('\n').filter(v=>!!v);
      this.multiValuesProductCode = [...new Set(this.multiValuesProductCode)];
      if(multipleRefProductCodeRef && multipleRefProductCodeRef.setValueArray){
        multipleRefProductCodeRef.setValueArray(this.multiValuesProductCode);
      }
    },
    getParam(){
      const params = {
        ...this.searchForm
      };
      delete params.shops;
      const getStr = value => value && Array.isArray(value) ? value.join("&#&") : undefined;
      if (this.multiValuesSellerSku.length > 0){
        params["sellerSkus"] = getStr(this.multiValuesSellerSku);
      }
      if (this.multiValuesProductCode.length > 0){
        params["productCodes"] = getStr(this.multiValuesProductCode);
      }
      params["sellerIds"] = getStr(this.searchForm.sellers);
      params["shopIds"] = getStr(this.searchForm.shops);
      params["spus"] = getStr(this.searchForm.spus);
      return params;
    },
    dateChange(date) {
      if (isEmpty(date)) {
        this.searchForm.startDate = '';
        this.searchForm.endDate = '';
      } else {
        this.searchForm.startDate = date[0];
        this.searchForm.endDate = date[1];
      }
    },
    //查询
    handleSearch() {
      const params = this.getParam();
      this.loading = true
      NewApply.listPage(params).then(res => {
        if (res && res['code'] === 0) {
          this.tableData = res.data.records;
          this.searchForm.total = parseInt(res.data.total);
        }
      }).finally(() => {
        this.loading = false
      })
    },
    handleReset() {
      //重置验证
      this.$refs['searchFormRef'].resetFields();
      this.searchForm.shops=[];
      this.searchForm.spus=[];
      this.searchForm.startDate=null;
      this.searchForm.endDate=null;
      this.date = [];
      this.resetMultiple(true);
    },
    resetMultiple(clearTxt = false) {
      if (clearTxt === true) {
        this.multiValuesSellerSku = [];
        const { multipleRefSellerSkuRef } = this.$refs;
        if (multipleRefSellerSkuRef && multipleRefSellerSkuRef.setValueArray) {
          multipleRefSellerSkuRef.setValueArray([]);
        }
        this.multiValuesProductCode = [];
        const { multipleRefProductCodeRef } = this.$refs;
        if (multipleRefProductCodeRef && multipleRefProductCodeRef.setValueArray) {
          multipleRefProductCodeRef.setValueArray([]);
        }
      }
      this.popContentSellerSku = undefined;
      this.popVisibleSellerSku = false;
      this.popContentProductCode = undefined;
      this.popVisibleProductCode = false;
    },
    handlePage(current) {
      this.searchForm.page = current
      this.handleSearch()
    },
    handlePageSize(size) {
      this.searchForm.limit = size
      this.handleSearch()
    },
    onSelectChange(selection){
      this.selectData = selection;
    },
    downTemplate() {
      this.loading = true;
      let params = {};
      params['fileName'] = "上新申请导入模板.xls";
      NewApply.downTemplate(params, () => {
        this.loading = false
      });
    },
    handleDel(){
      let ids = this.selectData.map(item => item['id']).join(',');
      if (!ids) {
        this.$message.error("请选择需要作废的记录");
        return;
      }
      let sellerSkuUsed = this.selectData.filter(item => item['status'] >= 2).map(item=>item['sellerSku']).join(',');
      this.$Modal.confirm({
        title: '提示',
        content: '您确认要删除这些数据吗？'+(!!sellerSkuUsed?("并且存在已匹配的销售SKU"+sellerSkuUsed):""),
        onOk: () => {
          this.loading = true;
          NewApply.remove({ids: ids}).then(res => {
            if (res['code'] === 0) {
              this.$Message.success('删除成功!');
              this.handleSearch();
            } else {
              this.$Message.error(res['message']);
            }
          }).finally(()=>this.loading=false)
        },
      })
    },
    handleExport(){
      this.loading = true;
      let params = this.getParam();
      params['fileName'] = "上新申请" + new Date().getTime() + ".xls";
      NewApply.exportFile(params, () => {
        this.loading = false
      });
    },
    addNewApply(){
      this.editVisible=true;
      const { newApplyEditRef } = this.$refs;
      if (newApplyEditRef) newApplyEditRef.setDefault(null,'Add');//给组件
    },
    noticeSellerId(){
      let ids = this.selectData.map(item => item['id']).join(',');
      if (!ids) {
        this.$message.error("请选择需要通知运营的记录");
        return;
      }
      this.loading = true;
      NewApply.noticeSeller({ids: ids}).then(res => {
        if (res['code'] === 0) {
          this.$Message.success('通知成功!');
        } else {
          this.$Message.error(res['message']);
        }
      }).finally(()=>this.loading=false)
    },
    refreshStatus(){
      let ids = this.selectData.map(item => item['id']).join(',');
      this.loading = true;
      NewApply.refreshStatus({ids: ids}).then(res => {
        if (res['code'] === 0) {
          this.$Message.success('运行成功!');
        } else {
          this.$Message.error(res['message']);
        }
      }).finally(()=>this.loading=false)
    },
    // 审批申请处理
    handleApply(row) {
      this.$Modal.confirm({
        title: '确认申请',
        content: `确定要为型号 "${row.spu}" 申请审批吗？`,
        onOk: () => {
          this.$set(row, 'reviewLoading', true);
          // 这里调用审批申请的API
          NewApply.apply({ id: row.id }).then(res => {
            if (res && res.code === 0) {
              this.$Message.success('审批申请提交成功！');
              this.handleSearch(); // 刷新列表
            } else {
              this.$Message.error(res.message || '审批申请失败');
            }
          }).catch(err => {
            this.$Message.error('审批申请失败：' + (err.message || '网络错误'));
          }).finally(() => {
            this.$set(row, 'reviewLoading', false);
          });
        }
      });
    },
    // 审批审核处理
    handleReview(row) {
      // 显示审核弹窗
      this.showReviewModal(row);
    },

    showReviewModal(row) {
      this.reviewForm = {
        id: row.id,
        spu: row.spu,
        agree: 'true', // 默认选择通过，使用字符串
        comment: '' // 审核意见
      };
      this.reviewModalVisible = true;
    },

    handleReviewSubmit() {
      this.$refs.reviewForm.validate((valid) => {
        if (valid) {
          this.reviewLoading = true;
          const params = {
            id: this.reviewForm.id,
            variables: {
              agree: this.reviewForm.agree === 'true', // 转换为布尔值
              comment: this.reviewForm.comment
            }
          };

          Workflow.review(params).then(res => {
            if (res && res.code === 0) {
              this.$Message.success('审批审核完成！');
              this.reviewModalVisible = false;
              this.handleSearch(); // 刷新列表
            } else {
              this.$Message.error(res.message || '审批审核失败');
            }
          }).catch(err => {
            this.$Message.error('审批审核失败：' + (err.message || '网络错误'));
          }).finally(() => {
            this.reviewLoading = false;
          });
        }
      });
    },

    handleReviewCancel() {
      this.reviewModalVisible = false;
      this.reviewForm = {
        id: null,
        spu: '',
        agree: 'true', // 使用字符串
        comment: ''
      };
    }
  }
};
</script>
<style lang="less">
.search-con-top{
  position: relative;
  padding: 0;
  //标签、sku、asin搜索项
  .multiClass{
    .flex-h{
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top:2px;
    }
  }
}
.widthClass {
  width: 350px
}
.buttonMargin{
  margin-left:15px;
}
.salesRank {
  .reportTable {
    .requestDetailColumn {
      .ivu-table-cell {
        padding-left: 2px;
        padding-right: 2px;

        .requestDetailTd {
          padding: 0 0;

          .ivu-row {
            border-bottom: 1px solid #e8eaec;

            &:last-child {
              border-bottom: none;
            }

            .ivu-col {
              border-right: 1px solid #e8eaec;
              padding: 3px 2px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;

              &:last-child {
                border-right: none;
              }
            }
          }
        }
      }
    }
  }
}
</style>
