package com.sy.erp.server.configuration;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 工作流专用的 Feign 配置
 * 解决 Activiti JavaDelegate 中 Feign 调用缺少认证信息的问题
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class WorkflowFeignConfiguration {

    @Value("${aimo.client.oauth2.admin.client-id:7gBZcbsC7kLIWCdELIl8nxcs}")
    private String clientId;

    @Value("${aimo.client.oauth2.admin.client-secret:0osTIhce7uPvDKHz6aa67bhCukaKoYl4}")
    private String clientSecret;

    /**
     * 工作流专用的 Feign 请求拦截器
     * 当没有 HTTP 请求上下文时，使用系统级别的认证信息
     */
    @Bean
    public RequestInterceptor workflowFeignRequestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                try {
                    // 尝试获取当前 HTTP 请求上下文
                    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                    
                    if (attributes != null) {
                        // 有 HTTP 上下文，使用原有的认证信息传递逻辑
                        HttpServletRequest request = attributes.getRequest();
                        String authorization = request.getHeader("Authorization");
                        if (authorization != null) {
                            template.header("Authorization", authorization);
                            log.debug("使用请求上下文中的认证信息: {}", authorization.substring(0, Math.min(20, authorization.length())) + "...");
                            return;
                        }
                    }
                    
                    // 没有 HTTP 上下文或没有认证信息，使用系统级别的认证
                    // 这里可以实现系统级别的认证逻辑，比如：
                    // 1. 使用固定的系统 token
                    // 2. 调用认证服务获取系统 token
                    // 3. 使用 noAuth 标记跳过认证
                    
                    // 方案1：使用 noAuth 标记（推荐用于内部服务调用）
                    template.header("noAuth", "true");
                    log.debug("工作流调用：使用 noAuth 标记跳过认证");

                    // 方案2：如果需要认证，可以在这里实现获取系统 token 的逻辑
                    // 注意：由于已经在 aimo-base-server 中将 /newApply/noticeUsers 添加到白名单，
                    // 这里的 noAuth 标记主要作为备用方案

                    log.info("工作流 Feign 调用：目标URL={}, 使用noAuth标记", template.url());
                    
                } catch (Exception e) {
                    log.warn("工作流 Feign 请求拦截器处理失败: {}", e.getMessage());
                    // 发生异常时，使用 noAuth 作为降级方案
                    template.header("noAuth", "true");
                }
            }
        };
    }

    /**
     * 获取系统级别的访问令牌
     * 这里可以实现具体的系统认证逻辑
     */
    private String getSystemToken() {
        // TODO: 实现系统级别的认证逻辑
        // 例如：调用认证服务，使用 client_credentials 模式获取 token
        return null;
    }
}
