package com.sy.erp.server;


import com.aimo.common.security.http.OpenRestTemplate;
import lombok.NonNull;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeansException;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;


/**
 * ERP服务数据
 * <AUTHOR>
 */
//@EnableCaching
@EnableFeignClients(basePackages = "com.sy.erp.server.feign")
@EnableDiscoveryClient
@SpringBootApplication
@MapperScan(basePackages = {"com.sy.erp.server.mapper"})
@ComponentScan(
        basePackages = "com.sy.*",
        excludeFilters = {@ComponentScan.Filter(
                type = FilterType.CUSTOM,
                classes = {AutoConfigurationExcludeFilter.class}
        )}
)
public class ErpApplication implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    public static void main(String[] args) {
        SpringApplication.run(ErpApplication.class, args);
        //刷新网关
        OpenRestTemplate openRestTemplate = ErpApplication.applicationContext.getBean(OpenRestTemplate.class);
        openRestTemplate.refreshGateway();
    }


    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        if (ErpApplication.applicationContext == null) {
            ErpApplication.applicationContext = applicationContext;
        }
    }
}
