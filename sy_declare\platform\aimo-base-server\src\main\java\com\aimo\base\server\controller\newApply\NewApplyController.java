package com.aimo.base.server.controller.newApply;

import com.aimo.base.client.constants.BaseConstants;
import com.aimo.base.client.model.newApply.NewApply;
import com.aimo.base.client.param.newApply.NewApplyParam;
import com.aimo.base.server.service.newApply.NewApplyService;
import com.aimo.common.exception.OpenAlertException;
import com.aimo.common.model.ResultBody;
import com.aimo.common.utils.CollectionUtils;
import com.aimo.common.utils.ExcelUtil;
import com.aimo.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

@Api(value = "上新管理配置", tags = "上新管理配置")
@RestController
@Slf4j
@RequestMapping(value = "/newApply")
public class NewApplyController {

    @Resource
    private NewApplyService newApplyService;

    @ApiOperation(value = "上新申请分页查询", notes = "上新申请分页查询")
    @GetMapping(value = "/listPage")
    @ResponseBody
    public ResultBody<IPage<NewApply>> listPage(NewApplyParam param) {
        return ResultBody.ok(newApplyService.listPage(param));
    }

    @ApiOperation(value = "上新申请删除", notes = "上新申请删除")
    @PostMapping(value = "/removeObj")
    @ResponseBody
    public ResultBody<Boolean> removeObj(@RequestParam(value = "ids") String ids) {
        List<Long> idList = StringUtils.toArrayLong(ids, null, true);
        if (CollectionUtils.isEmpty(idList)) {
            throw new OpenAlertException("数据不存在,请刷新后再操作");
        }
        return ResultBody.ok(newApplyService.removeObj(idList));
    }

    @ApiOperation(value = "上新申请作废", notes = "上新申请作废")
    @PostMapping(value = "/repealObj")
    @ResponseBody
    public ResultBody<Boolean> repealObj(@RequestParam(value = "ids") String ids) {
        List<Long> idList = StringUtils.toArrayLong(ids, null, true);
        if (CollectionUtils.isEmpty(idList)) {
            throw new OpenAlertException("数据不存在,请刷新后再操作");
        }
        UpdateWrapper<NewApply> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(NewApply::getId, idList).set(NewApply::getStatus, BaseConstants.STATUS2);
        return ResultBody.ok(newApplyService.update(updateWrapper));
    }

    @ApiOperation(value = "通知运营人员", notes = "通知运营人员")
    @PostMapping(value = "/noticeSeller")
    @ResponseBody
    public ResultBody<Boolean> noticeSeller(@RequestParam(value = "ids") String ids) {
        List<Long> idList = StringUtils.toArrayLong(ids, null, true);
        if (CollectionUtils.isEmpty(idList)) {
            throw new OpenAlertException("数据不存在,请刷新后再操作");
        }
        return ResultBody.ok(newApplyService.noticeSeller(idList));
    }

    @ApiOperation(value = "审批流通知接口", notes = "审批流通知接口")
    @PostMapping(value = "/noticeUsers")
    @ResponseBody
    public ResultBody<Boolean> noticeUsers(@RequestParam(value = "userIds") String userIds, @RequestParam String title, @RequestParam String content) {
        return ResultBody.ok(newApplyService.noticeUsers(userIds, title, content));
    }

    @ApiOperation(value = "下载上新申请", notes = "下载上新申请")
    @PostMapping("/exportFile")
    public void exportFile(HttpServletResponse response, NewApplyParam param) {
        newApplyService.exportFile(response, param);
    }

    @ApiOperation(value = "下载上新申请导入模板", notes = "下载上新申请导入模板")
    @GetMapping(value = "/downTemplate")
    public void downTemplate(HttpServletResponse response) {
        try {
            String fileName = "上新申请导入模板";
            InputStream input = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/NewApplyImportTemplate.xls");
            assert input != null;
            Workbook book = WorkbookFactory.create(input);
            input.close();
            ExcelUtil.export(response, book, fileName);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @ApiOperation(value = "导入上新申请表", notes = "Excel导入上新申请表")
    @PostMapping("/importFile")
    public ResultBody<Boolean> importFile(@RequestParam(value = "importFile") MultipartFile importFile) {
        return ResultBody.ok(newApplyService.importFile(importFile));
    }

    @ApiOperation(value = "生成销售SKU", notes = "生成销售SKU")
    @PostMapping(value = "/previewSellerSku")
    @ResponseBody
    public ResultBody<NewApply> previewSellerSku(@RequestBody NewApply newApply) {
        return ResultBody.ok(newApplyService.previewSellerSku(newApply));
    }

    @ApiOperation(value = "保存申请", notes = "保存申请")
    @PostMapping(value = "/saveApply")
    @ResponseBody
    public ResultBody<Boolean> saveApply(@RequestBody NewApply newApply) {
        return ResultBody.ok(newApplyService.saveApply(newApply));
    }

    @ApiOperation(value = "创建对照表", notes = "创建对照表")
    @PostMapping(value = "/createOrm")
    @ResponseBody
    public ResultBody<Boolean> createOrm(@RequestParam(value = "ids", required = false) String ids) {
        return ResultBody.ok(newApplyService.createOrm(StringUtils.toArrayLong(ids, null, true)));
    }

    @ApiOperation(value = "审核申请", notes = "审核申请")
    @PostMapping(value = "/review")
    @ResponseBody
    public ResultBody<Boolean> review(@RequestBody ReviewRequest request) {
        return ResultBody.ok(newApplyService.review(request.getId(), request.getVariables()));
    }

    /**
     * 审核请求参数
     */
    public static class ReviewRequest {
        private Long id;
        private java.util.Map<String, Object> variables;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public java.util.Map<String, Object> getVariables() {
            return variables;
        }

        public void setVariables(java.util.Map<String, Object> variables) {
            this.variables = variables;
        }
    }
}
