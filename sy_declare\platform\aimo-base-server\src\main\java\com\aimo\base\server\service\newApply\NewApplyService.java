package com.aimo.base.server.service.newApply;

import com.aimo.base.client.model.newApply.NewApply;
import com.aimo.base.client.param.newApply.NewApplyParam;
import com.aimo.common.mybatis.base.service.IBaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface NewApplyService extends IBaseService<NewApply> {
    NewApply previewSellerSku(NewApply newApply);

    Boolean saveApply(NewApply newApply);

    Boolean importFile(MultipartFile importFile);

    void exportFile(HttpServletResponse response, NewApplyParam param);

    IPage<NewApply> listPage(NewApplyParam param);

    Boolean removeObj(List<Long> idList);

    Boolean noticeSeller(List<Long> idList);

    Boolean noticeUsers(String userIds, String title, String content);

    Boolean createOrm(List<Long> idList);

    /**
     * 审核申请
     * @param id 申请ID
     * @param variables 审核变量（包含agree: true/false, comment: 审核意见等）
     * @return 是否成功
     */
    Boolean review(Long id, Map<String, Object> variables);
}
