package com.sy.erp.server.feign;

import com.aimo.base.client.constants.BaseConstants;
import com.aimo.base.client.service.IAiMoBaseClient;
import com.aimo.base.client.service.basic.ILingNoticeSyncServiceClient;
import com.aimo.common.model.ResultBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 新申请通知Feign客户端
 * 用于调用aimo-base-server的通知接口
 * 
 * <AUTHOR>
 */
@FeignClient(value = BaseConstants.BASE_SERVER)
public interface AiMoBaseClient extends IAiMoBaseClient {

}
