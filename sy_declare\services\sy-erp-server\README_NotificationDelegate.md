# NotificationDelegate 使用说明

## 概述
NotificationDelegate 是一个 Activiti 工作流委托类，用于在审批流程中发送通知。它通过 Feign 客户端调用 aimo-base-server 的通知接口。

## 功能特性
- 通过 Feign 调用 aimo-base-server 的 `/newApply/noticeUsers` 接口
- 支持向多个用户发送通知（用户ID以逗号分隔）
- 提供详细的错误处理和日志记录
- 预留了业务状态更新的扩展点

## 代码结构

### 1. Feign 客户端
文件：`com.sy.erp.server.feign.NewApplyNotificationClient`

```java
@FeignClient(value = BaseConstants.BASE_SERVER)
public interface NewApplyNotificationClient {
    @PostMapping(value = "/newApply/noticeUsers")
    ResultBody<Boolean> noticeUsers(@RequestParam(value = "userIds") String userIds,
                                   @RequestParam String title,
                                   @RequestParam String content);
}
```

### 2. 委托类
文件：`com.sy.erp.server.activiti.NotificationDelegate`

主要功能：
- 从工作流执行上下文中获取参数
- 调用 Feign 客户端发送通知
- 处理调用结果和异常

## 使用方法

### 在 Activiti 流程定义中使用
在 BPMN 流程定义中，可以这样配置服务任务：

```xml
<serviceTask id="notificationTask" name="发送通知" 
             activiti:delegateExpression="${notificationDelegate}">
  <extensionElements>
    <activiti:field name="recipients">
      <activiti:expression>${userIds}</activiti:expression>
    </activiti:field>
    <activiti:field name="notificationTitle">
      <activiti:expression>${title}</activiti:expression>
    </activiti:field>
    <activiti:field name="notificationContent">
      <activiti:expression>${content}</activiti:expression>
    </activiti:field>
  </extensionElements>
</serviceTask>
```

### 参数说明
- `recipients`: 接收通知的用户ID列表，多个用户ID用逗号分隔
- `notificationTitle`: 通知标题
- `notificationContent`: 通知内容

## 扩展功能

### 业务状态更新
在通知发送成功后，可以更新业务状态。代码中已预留扩展点：

```java
if (resultBody != null && resultBody.isOk() && resultBody.getData() != null && resultBody.getData()) {
    System.out.println("通知发送成功");
    // TODO: 在这里添加业务状态更新逻辑
    // 例如：
    // String businessId = (String) execution.getVariable("businessId");
    // businessService.updateStatus(businessId, 6);
}
```

## 依赖要求
- Spring Cloud OpenFeign
- aimo-base-client
- aimo-common-starter（包含 Feign 自动配置）

## 注意事项
1. 确保 ErpApplication 类上有 `@EnableFeignClients` 注解
2. 确保 aimo-base-server 服务正常运行且可访问
3. 字段名必须与 XML 配置完全一致（包括大小写）
4. 建议在生产环境中添加更详细的日志记录和监控
