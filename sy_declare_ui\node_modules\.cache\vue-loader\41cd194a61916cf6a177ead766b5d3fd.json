{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\newApply\\newApply\\newApply\\index.vue?vue&type=template&id=9bb720fa&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\newApply\\newApply\\newApply\\index.vue", "mtime": 1754279491441}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}