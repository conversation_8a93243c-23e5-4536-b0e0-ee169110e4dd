server:
    port: 8235
    servlet:
        session:
            cookie:
                name: OAUTH2SESSION
spring:
    application:
        name: sy-erp-server
    cloud:
        #手动配置Bus id,
        bus:
            id: ${spring.application.name}:${server.port}

        nacos:
            config:
                enabled: true
                username: nacos
                password: sy@nacos2022
                file-extension: properties
                shared-configs[0]:
                    data-id: common.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[1]:
                    data-id: db.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[2]:
                    data-id: redis.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[3]:
                    data-id: rabbitmq.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[4]:
                    data-id: workflow.properties
                    refresh: true
                    group: DEFAULT_GROUP

    # 数据源配置从Nacos的workflow.properties中获取
    # 本地只保留连接池配置，数据库连接信息从Nacos获取
    datasource:
        hikari:
            maximum-pool-size: 20
            minimum-idle: 5
            connection-timeout: 30000
            idle-timeout: 600000
            max-lifetime: 1800000
            leak-detection-threshold: 60000
            pool-name: ErpWorkflowPool

    main:
        allow-bean-definition-overriding: true
    #解决restful 404错误 spring.mvc.throw-exception-if-no-handler-found=true spring.resources.add-mappings=false
    mvc:
        throw-exception-if-no-handler-found: true
    resources:
        add-mappings: false
    profiles:
        active: local
      # 文件上传限制
    servlet:
        multipart:
            max-file-size: 10MB
            max-request-size: 10MB
    thymeleaf:
        cache: false
        encoding: UTF-8
        mode: LEGACYHTML5
        prefix: classpath:/templates/
        suffix: .html

management:
    endpoints:
        web:
            exposure:
                include: '*'

# Feign 和 Hystrix 配置
feign:
    hystrix:
        enabled: true
    compression:
        request:
            enabled: false
        response:
            enabled: false

# Hystrix 配置
hystrix:
    command:
        default:
            execution:
                isolation:
                    thread:
                        timeoutInMilliseconds: 10000
            circuitBreaker:
                enabled: true
                requestVolumeThreshold: 20
                sleepWindowInMilliseconds: 5000
                errorThresholdPercentage: 50
aimo:
    swagger2:
        enabled: true
        description: 平台用户认证服务器
        title: 平台用户认证服务器
    client:
        oauth2:
            admin:
                client-id: 7gBZcbsC7kLIWCdELIl8nxcs
                client-secret: 0osTIhce7uPvDKHz6aa67bhCukaKoYl4
#mybatis plus 设置
mybatis-plus:
 #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sy.erp.client.**.entity
  mapper-locations: classpath:mapper/*.xml