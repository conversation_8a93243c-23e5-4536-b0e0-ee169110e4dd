{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\newApply\\newApply\\newApply\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\newApply\\newApply\\newApply\\index.vue", "mtime": 1754279296931}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["autoTableHeight", "isEmpty", "ShopSelect", "SpuSelect", "Multiple", "personSelect", "NewApply", "Workflow", "NewApplyEdit", "getToken", "getUrl", "name", "components", "data", "h", "$createElement", "buttons", "type", "text", "title", "function", "lookData", "editData", "delData", "repealData", "loading", "sellerArr", "currentRow", "personVisible", "yesNoOps", "key", "multiValuesSellerSku", "popVisibleSellerSku", "popContentSellerSku", "undefined", "multiValuesProductCode", "popVisibleProductCode", "popContentProductCode", "importURl", "path", "loginInfo", "Accept", "mode", "Authorization", "selectData", "statusList", "date", "searchForm", "startDate", "endDate", "shops", "spus", "status", "page", "limit", "tableData", "columns", "width", "resizable", "render", "_", "_ref", "row", "value", "_ref2", "min<PERSON><PERSON><PERSON>", "_ref3", "_ref4", "slot", "_ref5", "_ref6", "_ref7", "align", "className", "_ref8", "_ref9", "popColumns", "_ref10", "statusText", "editVisible", "editTitle", "reviewModalVisible", "reviewLoading", "reviewForm", "id", "spu", "agree", "comment", "reviewFormRules", "required", "message", "trigger", "min", "max", "mounted", "handleSearch", "methods", "handleImportError", "err", "file", "$Message", "error", "handleImportSuccess", "res", "$refs", "clearFiles", "code", "handleMaxSize", "warning", "handleImportFormatError", "$Modal", "content", "okText", "open<PERSON>erson", "resetMultiple", "personSelectRef", "selectedIds", "sellers", "<PERSON><PERSON><PERSON><PERSON>", "filter", "v", "includes", "map", "nick<PERSON><PERSON>", "setSelectInfo", "info", "arguments", "length", "personArr", "clickMore", "_objectSpread", "handleDropDownClick", "clickRow", "button", "find", "btn", "isButtonDisabled", "getFilteredButtons", "reviewFlag", "getReviewStatusText", "getReviewStatusColor", "newApplyEditRef", "_this", "confirm", "onOk", "remove", "then", "$message", "finally", "_this2", "repeal", "closeDropdownSellerSku", "multipleRefSellerSkuRef", "trim", "replace", "split", "_toConsumableArray", "Set", "setValueArray", "closeDropdownProductCode", "multipleRefProductCodeRef", "getPara<PERSON>", "params", "getStr", "Array", "isArray", "join", "dateChange", "_this3", "listPage", "records", "total", "parseInt", "handleReset", "resetFields", "clearTxt", "handlePage", "current", "handlePageSize", "size", "onSelectChange", "selection", "downTemplate", "_this4", "handleDel", "_this5", "ids", "item", "sellerSkuUsed", "success", "handleExport", "_this6", "Date", "getTime", "exportFile", "addNewApply", "noticeSellerId", "_this7", "noticeSeller", "refreshStatus", "_this8", "handleApply", "_this9", "concat", "$set", "apply", "catch", "handleReview", "showReviewModal", "handleReviewSubmit", "_this10", "validate", "valid", "variables", "review", "handleReviewCancel"], "sources": ["src/view/module/newApply/newApply/newApply/index.vue"], "sourcesContent": ["<!--\r\n@create date 2025-02-07\r\n@desc 上新管理\r\n-->\r\n<template>\r\n  <div class=\"search-con-top salesRank\">\r\n    <Card :shadow=\"true\">\r\n      <div>\r\n        <Form ref=\"searchFormRef\" class=\"searchForm\" :model=\"searchForm\" :inline=\"true\" @submit.native.prevent>\r\n          <FormItem prop=\"date\">\r\n            <DatePicker type=\"daterange\" v-model=\"date\" placement=\"bottom-start\" @on-change=\"dateChange\"\r\n                        placeholder=\"申请开始日期-申请结束日期\" style=\"width: 200px\"></DatePicker>\r\n          </FormItem>\r\n          <FormItem prop=\"shop\">\r\n            <ShopSelect v-model=\"searchForm.shops\" placeholder=\"选择店铺\" width=\"205px\" valueField=\"id\" :isOverseas=\"true\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"sellers\" class=\"sellerSelectItem\">\r\n            <Select multiple type=\"text\" v-model=\"searchForm.sellers\" placeholder=\"销售员\" filterable :max-tag-count=\"1\" style=\"width: 233px\" :transfer=\"true\" >\r\n              <Option v-for=\"item in sellerArr\" :value=\"item.id\" :key=\"item.id\">{{ item.nickName }}</Option>\r\n            </Select>\r\n            <Button type=\"dashed\" @click=\"openPerson\" style=\"margin-left: 3px\" size=\"default\">选择</Button>\r\n            <person-select :visible=\"personVisible\" :onCancel=\"()=>personVisible=false\"\r\n                           @setPerson=\"arr => (searchForm.sellers = arr.map(v => v.id))\" @setSelectInfo=\"setSelectInfo\"\r\n                           ref=\"personSelectRef\" groupName=\"operations_persons\" :multiple=\"true\" :isQuery=\"true\" />\r\n          </FormItem>\r\n          <FormItem prop=\"spu\">\r\n            <SpuSelect v-model=\"searchForm.spus\" placeholder=\"选择型号\" width=\"205px\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"sellerSku\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入销售SKU(回车分隔)\" @changeValue=\"(values)=>{ multiValuesSellerSku = values || []; }\" ref=\"multipleRefSellerSkuRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleSellerSku=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleSellerSku\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentSellerSku\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownSellerSku\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"productCode\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品编码(回车分隔)\" @changeValue=\"(values)=>{ multiValuesProductCode = values || []; }\" ref=\"multipleRefProductCodeRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleProductCode=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleProductCode\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentProductCode\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownProductCode\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"status\" :clear=\"true\">\r\n            <Select type=\"text\" v-model=\"searchForm.status\" placeholder=\"状态\" style=\"width:160px\">\r\n              <Option v-for=\"(item,index) in statusList\" :value=\"item.key\" :key=\"index\">{{ item['value'] }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>&nbsp;\r\n            <Button @click=\"handleReset()\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </div>\r\n      <div style=\"margin-bottom: 10px;\" class=\"reportTable\">\r\n        <Button @click=\"addNewApply\" type=\"primary\"  :loading=\"loading\">上新申请</Button>\r\n        <div style=\"display:inline-block\">\r\n          <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                  :on-success=\"handleImportSuccess\"\r\n                  :format=\"['xls', 'xlsx']\"\r\n                  :show-upload-list=\"false\"\r\n                  :on-format-error=\"handleImportFormatError\"\r\n                  :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n            <Button  class=\"buttonMargin\" :loading=\"loading\">上传文件</Button>\r\n          </Upload></div>\r\n        <Button @click=\"downTemplate\" class=\"buttonMargin\" :loading=\"loading\">下载模板</Button>\r\n        <Button @click=\"noticeSellerId\" class=\"buttonMargin\" :loading=\"loading\">通知运营</Button>\r\n        <Button @click=\"refreshStatus\" class=\"buttonMargin\" :loading=\"loading\">检测生成</Button>\r\n        <Button @click=\"handleExport\" class=\"buttonMargin\" :loading=\"loading\">导出</Button>\r\n        <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n               :data=\"tableData\" :loading=\"loading\" @on-selection-change=\"onSelectChange\">\r\n          <template v-slot:status=\"{row}\">\r\n            <span v-for=\"(item, index) in statusList\" :key=\"index\" v-copytext=\"item['value']\"\r\n                        v-if=\"item['key'] === row['status']\">{{ item['value'] }}</span>\r\n          </template>\r\n          <template v-slot:isUpc=\"{row}\">\r\n            <span v-for=\"(item, index) in yesNoOps\" :key=\"index\" v-copytext=\"item['name']\"\r\n                  v-if=\"item['key'] === row['isUpc']\">{{ item['name'] }}</span>\r\n          </template>\r\n          <template v-slot:reviewStatus=\"{row}\">\r\n            <Tag :color=\"getReviewStatusColor(row.reviewFlag)\">\r\n              {{ getReviewStatusText(row.reviewFlag) }}\r\n            </Tag>\r\n          </template>\r\n          <template v-slot:review=\"{row}\">\r\n            <Button\r\n              v-if=\"row.runReviewStatus === 1\"\r\n              type=\"success\"\r\n              size=\"small\"\r\n              @click=\"handleReview(row)\"\r\n              :loading=\"row.reviewLoading\">\r\n              审核\r\n            </Button>\r\n          </template>\r\n          <template v-slot:detailList=\"{ row }\">\r\n            <div class=\"requestDetailTd\">\r\n              <Row>\r\n                <i-col span=\"7\">销售SKU</i-col>\r\n                <i-col span=\"3\">产品编码</i-col>\r\n                <i-col span=\"6\">产品规格</i-col>\r\n                <i-col span=\"4\">UPC码</i-col>\r\n                <i-col span=\"4\">是否刊登</i-col>\r\n              </Row>\r\n              <Row v-for=\"(item, index) in (row.detailList ? row.detailList.filter((_, i) => i < 3) : [])\" :key=\"index\">\r\n                <i-col span=\"7\" v-copytext=\"item['sellerSku']\" :title=\"item['sellerSku']\" style=\"padding: 3px 0;\">{{item['sellerSku']}}</i-col>\r\n                <i-col span=\"3\" v-copytext=\"item['productCode']\" :title=\"item['productCode']\">{{ item['productCode'] }}</i-col>\r\n                <i-col span=\"6\" v-copytext=\"item['productSpec']\" :title=\"item['productSpec']\">{{ item['productSpec'] }}</i-col>\r\n                <i-col span=\"4\" v-copytext=\"item['upcCode']\" :title=\"item['upcCode']\">{{ item['upcCode'] }}</i-col>\r\n                <i-col span=\"4\" v-copytext=\"item['status']===1?'已刊登':'未刊登'\">{{ item['status']===1?'已刊登':'未刊登' }}</i-col>\r\n              </Row>\r\n              <div style=\"text-align: right; padding-top: 5px; padding-right: 5px\"\r\n                   v-if=\"row.detailList && row.detailList.length > 0\">\r\n                <Poptip\r\n                  title=\"明细信息\"\r\n                  content=\"content\"\r\n                  placement=\"left-end\"\r\n                  trigger=\"click\"\r\n                  :transfer=\"true\"\r\n                  :max-width=\"550\">\r\n                  <a @click=\"clickMore(row)\">查看更多</a>\r\n                  <div slot=\"content\" style=\"padding-bottom: 8px; max-height: 500px\">\r\n                    <Table\r\n                      :columns=\"popColumns\"\r\n                      :border=\"true\"\r\n                      :data=\"row.detailList || []\"\r\n                      size=\"small\"\r\n                      :max-height=\"420\"/>\r\n                  </div>\r\n                </Poptip>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <template v-slot:action=\"{ row }\">\r\n            <div>\r\n              <template v-for=\"(item, index) in getFilteredButtons(row).length > 3? getFilteredButtons(row).slice(0, 2): getFilteredButtons(row)\">\r\n                <a v-if=\"!isButtonDisabled(item, row)\"\r\n                   @click=\"item.function(row)\"\r\n                   style=\"margin-right: 10px\"\r\n                   :title=\"item.title\"\r\n                   :key=\"'btn-' + index\">{{item.text }}</a>\r\n                <span v-else\r\n                      style=\"margin-right: 10px; color: #c5c8ce; cursor: not-allowed;\"\r\n                      :title=\"item.title + '(已禁用)'\"\r\n                      :key=\"'disabled-btn-' + index\">{{item.text }}</span>\r\n              </template>\r\n              <Dropdown :transfer=\"true\" ref=\"dropdownRef\" @on-click=\"handleDropDownClick($event, row)\" transfer-class-name=\"inventory-manage-down-btns\" v-if=\"getFilteredButtons(row).length > 3\">\r\n                <a href=\"javascript:void(0)\"><span>更多</span><Icon type=\"ios-arrow-down\"></Icon></a>\r\n                <DropdownMenu slot=\"list\">\r\n                  <DropdownItem v-for=\"(item, index) in getFilteredButtons(row).slice(2)\"\r\n                                :name=\"item.type\"\r\n                                :key=\"index\"\r\n                                :disabled=\"isButtonDisabled(item, row)\">{{item.text}}</DropdownItem>\r\n                </DropdownMenu>\r\n              </Dropdown>\r\n            </div>\r\n          </template>\r\n        </Table>\r\n        <Page :total=\"searchForm.total\" size=\"small\" :current=\"searchForm.page\" :page-size=\"searchForm.limit\" :show-elevator=\"true\"\r\n              :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n      </div>\r\n      <NewApplyEdit ref=\"newApplyEditRef\" :visible=\"editVisible\" :title=\"editTitle\" :onCancel=\"(value)=>{this.editVisible=false;if(value){this.handleSearch()}}\"></NewApplyEdit>\r\n\r\n      <!-- 审核弹窗 -->\r\n      <Modal\r\n        v-model=\"reviewModalVisible\"\r\n        title=\"审核申请\"\r\n        :mask-closable=\"false\"\r\n        :closable=\"false\"\r\n        width=\"500\">\r\n        <Form ref=\"reviewForm\" :model=\"reviewForm\" :rules=\"reviewFormRules\" :label-width=\"80\">\r\n          <FormItem label=\"型号\">\r\n            <span>{{ reviewForm.spu }}</span>\r\n          </FormItem>\r\n          <FormItem label=\"审核结果\" prop=\"agree\">\r\n            <RadioGroup v-model=\"reviewForm.agree\">\r\n              <Radio label=\"true\">通过</Radio>\r\n              <Radio label=\"false\">驳回</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"审核意见\" prop=\"comment\">\r\n            <Input\r\n              v-model=\"reviewForm.comment\"\r\n              type=\"textarea\"\r\n              :autosize=\"{minRows: 3, maxRows: 6}\"\r\n              placeholder=\"请输入审核意见\"\r\n              maxlength=\"200\"\r\n              show-word-limit />\r\n          </FormItem>\r\n        </Form>\r\n        <div slot=\"footer\">\r\n          <Button @click=\"handleReviewCancel\">取消</Button>\r\n          <Button type=\"primary\" @click=\"handleReviewSubmit\" :loading=\"reviewLoading\">确定</Button>\r\n        </div>\r\n      </Modal>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {autoTableHeight, isEmpty} from \"@/libs/tools.js\";\r\nimport ShopSelect from \"@/components/shopSelect/index.vue\";\r\nimport SpuSelect from \"@/components/spu/index.vue\";\r\nimport Multiple from \"@/view/module/common/multipleInput.vue\";\r\nimport personSelect from \"_c/person-select-radio/index.vue\";\r\nimport NewApply from \"@/api/newApply/newApply\";\r\nimport Workflow from \"@/api/base/workflow\";\r\nimport NewApplyEdit from \"./edit.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\nexport default {\r\n  name: \"newApply\",\r\n  components: {personSelect, Multiple, ShopSelect,SpuSelect,NewApplyEdit},\r\n  data() {\r\n    const buttons = [{type: \"look\", text: \"查看\", title: \"点击查看\", function: this.lookData},\r\n                    {type: \"edit\", text: \"修改\", title: \"点击修改\", function: this.editData},\r\n                    {type: \"repeal\", text: \"作废\", title: \"点击作废\", function: this.delData},\r\n                    {type: \"remove\", text: \"删除\", title: \"点击删除\", function: this.repealData},]\r\n    return {\r\n      autoTableHeight,\r\n      buttons,\r\n      loading: false,\r\n      sellerArr:[],\r\n      currentRow:null,\r\n      personVisible:false,\r\n      yesNoOps:[{ key:1, name: \"是\" }, { key:0, name: \"否\" }],\r\n      multiValuesSellerSku:[],\r\n      popVisibleSellerSku:false,\r\n      popContentSellerSku: undefined,\r\n      multiValuesProductCode:[],\r\n      popVisibleProductCode:false,\r\n      popContentProductCode: undefined,\r\n      importURl: getUrl() + NewApply.path+ '/importFile',\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      selectData:[],\r\n      statusList:[{\"key\":-1,\"value\":\"全部\"},{\"key\":0,\"value\":\"已申请\"},{\"key\":1,\"value\":\"已刊登\"},{\"key\":2,\"value\":\"已作废\"},{\"key\":3,\"value\":\"部分刊登\"}],\r\n      date:[],\r\n      searchForm: {\r\n        startDate:null,\r\n        endDate:null,\r\n        shops: [],\r\n        spus:[],\r\n        status:null,\r\n        page:1,\r\n        limit:10\r\n      },\r\n      tableData: [], //表格数据\r\n      columns: [{type: 'selection',width: 55,},\r\n        {title: '申请日期',key: 'sheetDate',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['sheetDate']}>{row['sheetDate']}</span>)},\r\n        {title: '店铺',key: 'shopName',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['shopName']}>{row['shopName']}</span>)},\r\n        {title: '型号',key: 'spu',minWidth: 150,resizable:true,render: (_, { row }) => (<span v-copytext={row['spu']}>{row['spu']}</span>)},\r\n        {title: '销售人员',key: 'sellerName',minWidth: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['sellerName']}>{row['sellerName']}</span>)},\r\n        {title: '是否需要UPC',key: 'isUpc',minWidth: 120,resizable:true,slot:'isUpc'},\r\n        {title: '审核阶段',key: 'review',minWidth: 100,resizable:true,slot:'review'},\r\n        {title: '运输模式',key: 'fulfillmentType',minWidth: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['fulfillmentType']}>{row['fulfillmentType']}</span>)},\r\n        {title: 'Listing简称',key: 'listingTitle',minWidth: 200,resizable:true,render: (_, { row }) => (<span v-copytext={row['listingTitle']}>{row['listingTitle']}</span>)},\r\n        {title: 'sku定位',key: 'skuLoc',minWidth: 200,resizable:true,render: (_, { row }) => (<span v-copytext={row['skuLoc']}>{row['skuLoc']}</span>)},\r\n        {title: \"明细\", align: \"center\", minWidth: 400, key: \"detailList\",slot: 'detailList', className: \"requestDetailColumn\"},\r\n        {title: '使用状态',key: 'status',minWidth: 100,resizable:true,slot:'status'},\r\n        {title: '审批状态',key: 'reviewFlag',minWidth: 100,resizable:true,slot:'reviewStatus'},\r\n        {title: '创建人',key: 'createUserName',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['createUserName']}>{row['createUserName']}</span>)},\r\n        {title: '创建时间',key: 'createTime',width: 160,resizable:true,render:(_, { row }) => (<span v-copytext={row['createTime']}>{row['createTime']}</span>)},\r\n        {title: '操作',key: 'operate',width: 200,resizable:true,slot:\"action\"},\r\n      ],\r\n      popColumns: [\r\n        {title: \"销售SKU\", align: \"center\", minWidth: 300, key: \"sellerSku\",},\r\n        {title: \"产品编码\", align: \"center\", minWidth: 150, key: \"productCode\",},\r\n        {title: \"产品规格\", align: \"center\", minWidth: 300, key: \"productSpec\",},\r\n        {title: \"UPC码\", align: \"center\", minWidth: 150, key: \"upcCode\",},\r\n        {\r\n          title: \"是否刊登\",\r\n          align: \"center\",\r\n          minWidth: 150,\r\n          key: \"status\",\r\n          resizable: true,\r\n          render: (_, { row }) => {\r\n            const statusText = row.status === 1 ? '已刊登' : '未刊登';\r\n            return <span v-copytext={statusText}>{statusText}</span>;\r\n          }\r\n        }\r\n      ],\r\n      editVisible:false,\r\n      editTitle:\"上新管理-新增\",\r\n      // 审核弹窗相关\r\n      reviewModalVisible: false,\r\n      reviewLoading: false,\r\n      reviewForm: {\r\n        id: null,\r\n        spu: '',\r\n        agree: 'true', // 使用字符串，对应Radio的label\r\n        comment: ''\r\n      },\r\n      reviewFormRules: {\r\n        comment: [\r\n          { required: true, message: '请输入审核意见', trigger: 'blur' },\r\n          { min: 1, max: 200, message: '审核意见长度在1到200个字符', trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  //组件初始化进行的操作\r\n  mounted() {\r\n    this.handleSearch();\r\n  },\r\n  methods: {\r\n    handleImportError (err, file) {\r\n      this.loading=false;\r\n      this.$Message.error(file.message);\r\n    },\r\n    handleImportSuccess (res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res.code === 0) {\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.error(res.message);\r\n      }\r\n    },\r\n    handleMaxSize () {\r\n      this.$Message.warning('大小不能超过10M.');\r\n    },\r\n    handleImportFormatError (file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    openPerson(){\r\n      this.resetMultiple();\r\n      //打开人员选择\r\n      const { personSelectRef } = this.$refs;\r\n      const { sellerArr, searchForm } = this;\r\n      const selectedIds = searchForm.sellers || [];\r\n      if (personSelectRef) personSelectRef.setDefault(\r\n        sellerArr.filter(v => selectedIds.includes(v.id)).map(v=>({ name: v.nickName, id: v.id }))\r\n      );//给组件设置默认选中\r\n      this.personVisible = true;\r\n    },\r\n    // 人员选择相关\r\n    setSelectInfo(info={}){\r\n      this.sellerArr = info.personArr || [];\r\n    },\r\n    clickMore(row = {}) {\r\n      this.currentRow = {...row};\r\n    },\r\n    //操作\r\n    handleDropDownClick(name, row = {}) {\r\n      this.clickRow = { ...row };\r\n      // 检查按钮是否被禁用\r\n      const button = this.buttons.find(btn => btn.type === name);\r\n      if (button && this.isButtonDisabled(button, row)) {\r\n        this.$Message.warning('当前状态下该操作不可用');\r\n        return;\r\n      }\r\n\r\n      switch (name) {\r\n        case \"look\":\r\n          this.lookData(row);\r\n          break;\r\n        case \"edit\":\r\n          this.editData(row);\r\n          break;\r\n        case \"remove\":\r\n          this.delData(row);\r\n          break;\r\n        case \"repeal\":\r\n          this.repealData(row);\r\n          break;\r\n        default:\r\n      }\r\n    },\r\n    // 获取过滤后的按钮列表\r\n    getFilteredButtons(row) {\r\n      return this.buttons;\r\n    },\r\n    // 判断按钮是否应该被禁用\r\n    isButtonDisabled(button, row) {\r\n      // 当reviewFlag为1时，禁用修改、作废、删除按钮\r\n      if (row.reviewFlag === 1 && row.status !== 5) {\r\n        // , 'remove'\r\n        return ['edit', 'repeal'].includes(button.type);\r\n      }\r\n      return false;\r\n    },\r\n    // 获取审批状态文本\r\n    getReviewStatusText(reviewFlag) {\r\n      switch (reviewFlag) {\r\n        case 0:\r\n          return '无需审批';\r\n        case 1:\r\n          return '需要审';\r\n        default:\r\n          return '未知状态';\r\n      }\r\n    },\r\n    // 获取审批状态颜色\r\n    getReviewStatusColor(reviewFlag) {\r\n      switch (reviewFlag) {\r\n        case 0:\r\n          return 'default';\r\n        case 1:\r\n          return 'orange';\r\n        default:\r\n          return 'default';\r\n      }\r\n    },\r\n    editData(row){\r\n      // 检查是否被禁用\r\n      if (row.reviewFlag === 1) {\r\n        this.$Message.warning('当前记录正在审批流程中，无法修改');\r\n        return;\r\n      }\r\n      this.editVisible = true;\r\n      const { newApplyEditRef } = this.$refs;\r\n      if (newApplyEditRef) newApplyEditRef.setDefault(row,'Edit');//给组件\r\n    },\r\n    lookData(row){\r\n      this.editVisible = true;\r\n      const { newApplyEditRef } = this.$refs;\r\n      if (newApplyEditRef) newApplyEditRef.setDefault(row,'View');//给组件\r\n    },\r\n    delData(row){\r\n      // 检查是否被禁用\r\n      // if (row.reviewFlag === 1) {\r\n      //   this.$Message.warning('当前记录正在审批流程中，无法删除');\r\n      //   return;\r\n      // }\r\n      this.loading=true;\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这些数据吗？',\r\n        onOk: () => {\r\n          NewApply.remove({\"ids\":row.id}).then(res=>{\r\n            if(res && res['code'] === 0){\r\n              this.handleSearch();\r\n            }else{\r\n              this.$message.error(res['message']);\r\n            }\r\n          }).finally(()=>{this.loading=false;})\r\n        },\r\n      })\r\n    },\r\n    repealData(row){\r\n      // 检查是否被禁用\r\n      if (row.reviewFlag === 1) {\r\n        this.$Message.warning('当前记录正在审批流程中，无法作废');\r\n        return;\r\n      }\r\n      this.loading=true;\r\n      NewApply.repeal({\"ids\":row.id}).then(res=>{\r\n        if(res && res['code'] === 0){\r\n          this.handleSearch();\r\n        }else{\r\n          this.$message.error(res['message']);\r\n        }\r\n      }).finally(()=>{this.loading=false;})\r\n    },\r\n    closeDropdownSellerSku() { //关闭输入文本框\r\n      const { popContentSellerSku } = this;\r\n      const { multipleRefSellerSkuRef } = this.$refs;\r\n      this.popVisibleSellerSku = false;\r\n      if(!popContentSellerSku) return;\r\n      const content = popContentSellerSku ? popContentSellerSku.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesSellerSku = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesSellerSku = [...new Set(this.multiValuesSellerSku)];\r\n      if(multipleRefSellerSkuRef && multipleRefSellerSkuRef.setValueArray){\r\n        multipleRefSellerSkuRef.setValueArray(this.multiValuesSellerSku);\r\n      }\r\n    },\r\n    closeDropdownProductCode() { //关闭输入文本框\r\n      const { popContentProductCode } = this;\r\n      const { multipleRefProductCodeRef } = this.$refs;\r\n      this.popVisibleProductCode = false;\r\n      if(!popContentProductCode) return;\r\n      const content = popContentProductCode ? popContentProductCode.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesProductCode = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesProductCode = [...new Set(this.multiValuesProductCode)];\r\n      if(multipleRefProductCodeRef && multipleRefProductCodeRef.setValueArray){\r\n        multipleRefProductCodeRef.setValueArray(this.multiValuesProductCode);\r\n      }\r\n    },\r\n    getParam(){\r\n      const params = {\r\n        ...this.searchForm\r\n      };\r\n      delete params.shops;\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\"&#&\") : undefined;\r\n      if (this.multiValuesSellerSku.length > 0){\r\n        params[\"sellerSkus\"] = getStr(this.multiValuesSellerSku);\r\n      }\r\n      if (this.multiValuesProductCode.length > 0){\r\n        params[\"productCodes\"] = getStr(this.multiValuesProductCode);\r\n      }\r\n      params[\"sellerIds\"] = getStr(this.searchForm.sellers);\r\n      params[\"shopIds\"] = getStr(this.searchForm.shops);\r\n      params[\"spus\"] = getStr(this.searchForm.spus);\r\n      return params;\r\n    },\r\n    dateChange(date) {\r\n      if (isEmpty(date)) {\r\n        this.searchForm.startDate = '';\r\n        this.searchForm.endDate = '';\r\n      } else {\r\n        this.searchForm.startDate = date[0];\r\n        this.searchForm.endDate = date[1];\r\n      }\r\n    },\r\n    //查询\r\n    handleSearch() {\r\n      const params = this.getParam();\r\n      this.loading = true\r\n      NewApply.listPage(params).then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.tableData = res.data.records;\r\n          this.searchForm.total = parseInt(res.data.total);\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    handleReset() {\r\n      //重置验证\r\n      this.$refs['searchFormRef'].resetFields();\r\n      this.searchForm.shops=[];\r\n      this.searchForm.spus=[];\r\n      this.searchForm.startDate=null;\r\n      this.searchForm.endDate=null;\r\n      this.date = [];\r\n      this.resetMultiple(true);\r\n    },\r\n    resetMultiple(clearTxt = false) {\r\n      if (clearTxt === true) {\r\n        this.multiValuesSellerSku = [];\r\n        const { multipleRefSellerSkuRef } = this.$refs;\r\n        if (multipleRefSellerSkuRef && multipleRefSellerSkuRef.setValueArray) {\r\n          multipleRefSellerSkuRef.setValueArray([]);\r\n        }\r\n        this.multiValuesProductCode = [];\r\n        const { multipleRefProductCodeRef } = this.$refs;\r\n        if (multipleRefProductCodeRef && multipleRefProductCodeRef.setValueArray) {\r\n          multipleRefProductCodeRef.setValueArray([]);\r\n        }\r\n      }\r\n      this.popContentSellerSku = undefined;\r\n      this.popVisibleSellerSku = false;\r\n      this.popContentProductCode = undefined;\r\n      this.popVisibleProductCode = false;\r\n    },\r\n    handlePage(current) {\r\n      this.searchForm.page = current\r\n      this.handleSearch()\r\n    },\r\n    handlePageSize(size) {\r\n      this.searchForm.limit = size\r\n      this.handleSearch()\r\n    },\r\n    onSelectChange(selection){\r\n      this.selectData = selection;\r\n    },\r\n    downTemplate() {\r\n      this.loading = true;\r\n      let params = {};\r\n      params['fileName'] = \"上新申请导入模板.xls\";\r\n      NewApply.downTemplate(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    handleDel(){\r\n      let ids = this.selectData.map(item => item['id']).join(',');\r\n      if (!ids) {\r\n        this.$message.error(\"请选择需要作废的记录\");\r\n        return;\r\n      }\r\n      let sellerSkuUsed = this.selectData.filter(item => item['status'] >= 2).map(item=>item['sellerSku']).join(',');\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这些数据吗？'+(!!sellerSkuUsed?(\"并且存在已匹配的销售SKU\"+sellerSkuUsed):\"\"),\r\n        onOk: () => {\r\n          this.loading = true;\r\n          NewApply.remove({ids: ids}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.$Message.success('删除成功!');\r\n              this.handleSearch();\r\n            } else {\r\n              this.$Message.error(res['message']);\r\n            }\r\n          }).finally(()=>this.loading=false)\r\n        },\r\n      })\r\n    },\r\n    handleExport(){\r\n      this.loading = true;\r\n      let params = this.getParam();\r\n      params['fileName'] = \"上新申请\" + new Date().getTime() + \".xls\";\r\n      NewApply.exportFile(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    addNewApply(){\r\n      this.editVisible=true;\r\n      const { newApplyEditRef } = this.$refs;\r\n      if (newApplyEditRef) newApplyEditRef.setDefault(null,'Add');//给组件\r\n    },\r\n    noticeSellerId(){\r\n      let ids = this.selectData.map(item => item['id']).join(',');\r\n      if (!ids) {\r\n        this.$message.error(\"请选择需要通知运营的记录\");\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      NewApply.noticeSeller({ids: ids}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.$Message.success('通知成功!');\r\n        } else {\r\n          this.$Message.error(res['message']);\r\n        }\r\n      }).finally(()=>this.loading=false)\r\n    },\r\n    refreshStatus(){\r\n      let ids = this.selectData.map(item => item['id']).join(',');\r\n      this.loading = true;\r\n      NewApply.refreshStatus({ids: ids}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.$Message.success('运行成功!');\r\n        } else {\r\n          this.$Message.error(res['message']);\r\n        }\r\n      }).finally(()=>this.loading=false)\r\n    },\r\n    // 审批申请处理\r\n    handleApply(row) {\r\n      this.$Modal.confirm({\r\n        title: '确认申请',\r\n        content: `确定要为型号 \"${row.spu}\" 申请审批吗？`,\r\n        onOk: () => {\r\n          this.$set(row, 'reviewLoading', true);\r\n          // 这里调用审批申请的API\r\n          NewApply.apply({ id: row.id }).then(res => {\r\n            if (res && res.code === 0) {\r\n              this.$Message.success('审批申请提交成功！');\r\n              this.handleSearch(); // 刷新列表\r\n            } else {\r\n              this.$Message.error(res.message || '审批申请失败');\r\n            }\r\n          }).catch(err => {\r\n            this.$Message.error('审批申请失败：' + (err.message || '网络错误'));\r\n          }).finally(() => {\r\n            this.$set(row, 'reviewLoading', false);\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 审批审核处理\r\n    handleReview(row) {\r\n      // 显示审核弹窗\r\n      this.showReviewModal(row);\r\n    },\r\n\r\n    showReviewModal(row) {\r\n      this.reviewForm = {\r\n        id: row.id,\r\n        spu: row.spu,\r\n        agree: 'true', // 默认选择通过，使用字符串\r\n        comment: '' // 审核意见\r\n      };\r\n      this.reviewModalVisible = true;\r\n    },\r\n\r\n    handleReviewSubmit() {\r\n      this.$refs.reviewForm.validate((valid) => {\r\n        if (valid) {\r\n          this.reviewLoading = true;\r\n          const params = {\r\n            id: this.reviewForm.id,\r\n            variables: {\r\n              agree: this.reviewForm.agree === 'true', // 转换为布尔值\r\n              comment: this.reviewForm.comment\r\n            }\r\n          };\r\n\r\n          Workflow.review(params).then(res => {\r\n            if (res && res.code === 0) {\r\n              this.$Message.success('审批审核完成！');\r\n              this.reviewModalVisible = false;\r\n              this.handleSearch(); // 刷新列表\r\n            } else {\r\n              this.$Message.error(res.message || '审批审核失败');\r\n            }\r\n          }).catch(err => {\r\n            this.$Message.error('审批审核失败：' + (err.message || '网络错误'));\r\n          }).finally(() => {\r\n            this.reviewLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    handleReviewCancel() {\r\n      this.reviewModalVisible = false;\r\n      this.reviewForm = {\r\n        id: null,\r\n        spu: '',\r\n        agree: 'true', // 使用字符串\r\n        comment: ''\r\n      };\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.search-con-top{\r\n  position: relative;\r\n  padding: 0;\r\n  //标签、sku、asin搜索项\r\n  .multiClass{\r\n    .flex-h{\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-top:2px;\r\n    }\r\n  }\r\n}\r\n.widthClass {\r\n  width: 350px\r\n}\r\n.buttonMargin{\r\n  margin-left:15px;\r\n}\r\n.salesRank {\r\n  .reportTable {\r\n    .requestDetailColumn {\r\n      .ivu-table-cell {\r\n        padding-left: 2px;\r\n        padding-right: 2px;\r\n\r\n        .requestDetailTd {\r\n          padding: 0 0;\r\n\r\n          .ivu-row {\r\n            border-bottom: 1px solid #e8eaec;\r\n\r\n            &:last-child {\r\n              border-bottom: none;\r\n            }\r\n\r\n            .ivu-col {\r\n              border-right: 1px solid #e8eaec;\r\n              padding: 3px 2px;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n\r\n              &:last-child {\r\n                border-right: none;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAwNA,SAAAA,eAAA,EAAAC,OAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,OAAAC,QAAA;AACA,OAAAC,YAAA;AACA,OAAAC,QAAA;AACA,OAAAC,QAAA;AACA,OAAAC,YAAA;AACA,SAAAC,QAAA,EAAAC,MAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAP,YAAA,EAAAA,YAAA;IAAAD,QAAA,EAAAA,QAAA;IAAAF,UAAA,EAAAA,UAAA;IAAAC,SAAA,EAAAA,SAAA;IAAAK,YAAA,EAAAA;EAAA;EACAK,IAAA,WAAAA,KAAA;IAAA,IAAAC,CAAA,QAAAC,cAAA;IACA,IAAAC,OAAA;MAAAC,IAAA;MAAAC,IAAA;MAAAC,KAAA;MAAAC,QAAA,OAAAC;IAAA,GACA;MAAAJ,IAAA;MAAAC,IAAA;MAAAC,KAAA;MAAAC,QAAA,OAAAE;IAAA,GACA;MAAAL,IAAA;MAAAC,IAAA;MAAAC,KAAA;MAAAC,QAAA,OAAAG;IAAA,GACA;MAAAN,IAAA;MAAAC,IAAA;MAAAC,KAAA;MAAAC,QAAA,OAAAI;IAAA;IACA;MACAxB,eAAA,EAAAA,eAAA;MACAgB,OAAA,EAAAA,OAAA;MACAS,OAAA;MACAC,SAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;QAAAC,GAAA;QAAAnB,IAAA;MAAA;QAAAmB,GAAA;QAAAnB,IAAA;MAAA;MACAoB,oBAAA;MACAC,mBAAA;MACAC,mBAAA,EAAAC,SAAA;MACAC,sBAAA;MACAC,qBAAA;MACAC,qBAAA,EAAAH,SAAA;MACAI,SAAA,EAAA5B,MAAA,KAAAJ,QAAA,CAAAiC,IAAA;MACAC,SAAA;QACAC,MAAA;QACAC,IAAA;QACAC,aAAA,cAAAlC,QAAA;MACA;MACAmC,UAAA;MACAC,UAAA;QAAA;QAAA;MAAA;QAAA;QAAA;MAAA;QAAA;QAAA;MAAA;QAAA;QAAA;MAAA;QAAA;QAAA;MAAA;MACAC,IAAA;MACAC,UAAA;QACAC,SAAA;QACAC,OAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,SAAA;MAAA;MACAC,OAAA;QAAAvC,IAAA;QAAAwC,KAAA;MAAA,GACA;QAAAtC,KAAA;QAAAW,GAAA;QAAA2B,KAAA;QAAAC,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAC,IAAA;UAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAA2B,KAAA;QAAAC,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAI,KAAA;UAAA,IAAAF,GAAA,GAAAE,KAAA,CAAAF,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAM,KAAA;UAAA,IAAAJ,GAAA,GAAAI,KAAA,CAAAJ,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAO,KAAA;UAAA,IAAAL,GAAA,GAAAK,KAAA,CAAAL,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAU,IAAA;MAAA,GACA;QAAAjD,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAU,IAAA;MAAA,GACA;QAAAjD,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAS,KAAA;UAAA,IAAAP,GAAA,GAAAO,KAAA,CAAAP,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAU,KAAA;UAAA,IAAAR,GAAA,GAAAQ,KAAA,CAAAR,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAW,KAAA;UAAA,IAAAT,GAAA,GAAAS,KAAA,CAAAT,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAqD,KAAA;QAAAP,QAAA;QAAAnC,GAAA;QAAAsC,IAAA;QAAAK,SAAA;MAAA,GACA;QAAAtD,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAU,IAAA;MAAA,GACA;QAAAjD,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAU,IAAA;MAAA,GACA;QAAAjD,KAAA;QAAAW,GAAA;QAAA2B,KAAA;QAAAC,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAc,KAAA;UAAA,IAAAZ,GAAA,GAAAY,KAAA,CAAAZ,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAA2B,KAAA;QAAAC,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAe,KAAA;UAAA,IAAAb,GAAA,GAAAa,KAAA,CAAAb,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAA2B,KAAA;QAAAC,SAAA;QAAAU,IAAA;MAAA,EACA;MACAQ,UAAA,GACA;QAAAzD,KAAA;QAAAqD,KAAA;QAAAP,QAAA;QAAAnC,GAAA;MAAA,GACA;QAAAX,KAAA;QAAAqD,KAAA;QAAAP,QAAA;QAAAnC,GAAA;MAAA,GACA;QAAAX,KAAA;QAAAqD,KAAA;QAAAP,QAAA;QAAAnC,GAAA;MAAA,GACA;QAAAX,KAAA;QAAAqD,KAAA;QAAAP,QAAA;QAAAnC,GAAA;MAAA,GACA;QACAX,KAAA;QACAqD,KAAA;QACAP,QAAA;QACAnC,GAAA;QACA4B,SAAA;QACAC,MAAA,WAAAA,OAAAC,CAAA,EAAAiB,MAAA;UAAA,IAAAf,GAAA,GAAAe,MAAA,CAAAf,GAAA;UACA,IAAAgB,UAAA,GAAAhB,GAAA,CAAAV,MAAA;UACA,OAAAtC,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAe;YAAA;UAAA,IAAAA,UAAA;QACA;MACA,EACA;MACAC,WAAA;MACAC,SAAA;MACA;MACAC,kBAAA;MACAC,aAAA;MACAC,UAAA;QACAC,EAAA;QACAC,GAAA;QACAC,KAAA;QAAA;QACAC,OAAA;MACA;MACAC,eAAA;QACAD,OAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACAC,iBAAA,WAAAA,kBAAAC,GAAA,EAAAC,IAAA;MACA,KAAA1E,OAAA;MACA,KAAA2E,QAAA,CAAAC,KAAA,CAAAF,IAAA,CAAAT,OAAA;IACA;IACAY,mBAAA,WAAAA,oBAAAC,GAAA;MACA,KAAAC,KAAA,kBAAAC,UAAA;MACA,IAAAF,GAAA,CAAAG,IAAA;QACA,KAAAX,YAAA;MACA;QACA,KAAAK,QAAA,CAAAC,KAAA,CAAAE,GAAA,CAAAb,OAAA;MACA;IACA;IACAiB,aAAA,WAAAA,cAAA;MACA,KAAAP,QAAA,CAAAQ,OAAA;IACA;IACAC,uBAAA,WAAAA,wBAAAV,IAAA;MACA;MACA,KAAAW,MAAA,CAAAT,KAAA;QACAlF,KAAA;QACA4F,OAAA,UAAAZ,IAAA,CAAAxF,IAAA;QACAqG,MAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,aAAA;MACA;MACA,IAAAC,eAAA,QAAAX,KAAA,CAAAW,eAAA;MACA,IAAAzF,SAAA,QAAAA,SAAA;QAAAqB,UAAA,QAAAA,UAAA;MACA,IAAAqE,WAAA,GAAArE,UAAA,CAAAsE,OAAA;MACA,IAAAF,eAAA,EAAAA,eAAA,CAAAG,UAAA,CACA5F,SAAA,CAAA6F,MAAA,WAAAC,CAAA;QAAA,OAAAJ,WAAA,CAAAK,QAAA,CAAAD,CAAA,CAAApC,EAAA;MAAA,GAAAsC,GAAA,WAAAF,CAAA;QAAA;UAAA7G,IAAA,EAAA6G,CAAA,CAAAG,QAAA;UAAAvC,EAAA,EAAAoC,CAAA,CAAApC;QAAA;MAAA,EACA;MACA,KAAAxD,aAAA;IACA;IACA;IACAgG,aAAA,WAAAA,cAAA;MAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA5F,SAAA,GAAA4F,SAAA;MACA,KAAApG,SAAA,GAAAmG,IAAA,CAAAG,SAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAnE,GAAA,GAAAgE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA5F,SAAA,GAAA4F,SAAA;MACA,KAAAnG,UAAA,GAAAuG,aAAA,KAAApE,GAAA;IACA;IACA;IACAqE,mBAAA,WAAAA,oBAAAxH,IAAA;MAAA,IAAAmD,GAAA,GAAAgE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA5F,SAAA,GAAA4F,SAAA;MACA,KAAAM,QAAA,GAAAF,aAAA,KAAApE,GAAA;MACA;MACA,IAAAuE,MAAA,QAAArH,OAAA,CAAAsH,IAAA,WAAAC,GAAA;QAAA,OAAAA,GAAA,CAAAtH,IAAA,KAAAN,IAAA;MAAA;MACA,IAAA0H,MAAA,SAAAG,gBAAA,CAAAH,MAAA,EAAAvE,GAAA;QACA,KAAAsC,QAAA,CAAAQ,OAAA;QACA;MACA;MAEA,QAAAjG,IAAA;QACA;UACA,KAAAU,QAAA,CAAAyC,GAAA;UACA;QACA;UACA,KAAAxC,QAAA,CAAAwC,GAAA;UACA;QACA;UACA,KAAAvC,OAAA,CAAAuC,GAAA;UACA;QACA;UACA,KAAAtC,UAAA,CAAAsC,GAAA;UACA;QACA;MACA;IACA;IACA;IACA2E,kBAAA,WAAAA,mBAAA3E,GAAA;MACA,YAAA9C,OAAA;IACA;IACA;IACAwH,gBAAA,WAAAA,iBAAAH,MAAA,EAAAvE,GAAA;MACA;MACA,IAAAA,GAAA,CAAA4E,UAAA,UAAA5E,GAAA,CAAAV,MAAA;QACA;QACA,0BAAAqE,QAAA,CAAAY,MAAA,CAAApH,IAAA;MACA;MACA;IACA;IACA;IACA0H,mBAAA,WAAAA,oBAAAD,UAAA;MACA,QAAAA,UAAA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACA;IACAE,oBAAA,WAAAA,qBAAAF,UAAA;MACA,QAAAA,UAAA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACApH,QAAA,WAAAA,SAAAwC,GAAA;MACA;MACA,IAAAA,GAAA,CAAA4E,UAAA;QACA,KAAAtC,QAAA,CAAAQ,OAAA;QACA;MACA;MACA,KAAA7B,WAAA;MACA,IAAA8D,eAAA,QAAArC,KAAA,CAAAqC,eAAA;MACA,IAAAA,eAAA,EAAAA,eAAA,CAAAvB,UAAA,CAAAxD,GAAA;IACA;IACAzC,QAAA,WAAAA,SAAAyC,GAAA;MACA,KAAAiB,WAAA;MACA,IAAA8D,eAAA,QAAArC,KAAA,CAAAqC,eAAA;MACA,IAAAA,eAAA,EAAAA,eAAA,CAAAvB,UAAA,CAAAxD,GAAA;IACA;IACAvC,OAAA,WAAAA,QAAAuC,GAAA;MAAA,IAAAgF,KAAA;MACA;MACA;MACA;MACA;MACA;MACA,KAAArH,OAAA;MACA,KAAAqF,MAAA,CAAAiC,OAAA;QACA5H,KAAA;QACA4F,OAAA;QACAiC,IAAA,WAAAA,KAAA;UACA1I,QAAA,CAAA2I,MAAA;YAAA,OAAAnF,GAAA,CAAAsB;UAAA,GAAA8D,IAAA,WAAA3C,GAAA;YACA,IAAAA,GAAA,IAAAA,GAAA;cACAuC,KAAA,CAAA/C,YAAA;YACA;cACA+C,KAAA,CAAAK,QAAA,CAAA9C,KAAA,CAAAE,GAAA;YACA;UACA,GAAA6C,OAAA;YAAAN,KAAA,CAAArH,OAAA;UAAA;QACA;MACA;IACA;IACAD,UAAA,WAAAA,WAAAsC,GAAA;MAAA,IAAAuF,MAAA;MACA;MACA,IAAAvF,GAAA,CAAA4E,UAAA;QACA,KAAAtC,QAAA,CAAAQ,OAAA;QACA;MACA;MACA,KAAAnF,OAAA;MACAnB,QAAA,CAAAgJ,MAAA;QAAA,OAAAxF,GAAA,CAAAsB;MAAA,GAAA8D,IAAA,WAAA3C,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA;UACA8C,MAAA,CAAAtD,YAAA;QACA;UACAsD,MAAA,CAAAF,QAAA,CAAA9C,KAAA,CAAAE,GAAA;QACA;MACA,GAAA6C,OAAA;QAAAC,MAAA,CAAA5H,OAAA;MAAA;IACA;IACA8H,sBAAA,WAAAA,uBAAA;MAAA;MACA,IAAAtH,mBAAA,QAAAA,mBAAA;MACA,IAAAuH,uBAAA,QAAAhD,KAAA,CAAAgD,uBAAA;MACA,KAAAxH,mBAAA;MACA,KAAAC,mBAAA;MACA,IAAA8E,OAAA,GAAA9E,mBAAA,GAAAA,mBAAA,CAAAwH,IAAA,GAAAC,OAAA;MACA,KAAA3H,oBAAA,GAAAgF,OAAA,CAAA4C,KAAA,OAAApC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,KAAAzF,oBAAA,GAAA6H,kBAAA,KAAAC,GAAA,MAAA9H,oBAAA;MACA,IAAAyH,uBAAA,IAAAA,uBAAA,CAAAM,aAAA;QACAN,uBAAA,CAAAM,aAAA,MAAA/H,oBAAA;MACA;IACA;IACAgI,wBAAA,WAAAA,yBAAA;MAAA;MACA,IAAA1H,qBAAA,QAAAA,qBAAA;MACA,IAAA2H,yBAAA,QAAAxD,KAAA,CAAAwD,yBAAA;MACA,KAAA5H,qBAAA;MACA,KAAAC,qBAAA;MACA,IAAA0E,OAAA,GAAA1E,qBAAA,GAAAA,qBAAA,CAAAoH,IAAA,GAAAC,OAAA;MACA,KAAAvH,sBAAA,GAAA4E,OAAA,CAAA4C,KAAA,OAAApC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,KAAArF,sBAAA,GAAAyH,kBAAA,KAAAC,GAAA,MAAA1H,sBAAA;MACA,IAAA6H,yBAAA,IAAAA,yBAAA,CAAAF,aAAA;QACAE,yBAAA,CAAAF,aAAA,MAAA3H,sBAAA;MACA;IACA;IACA8H,QAAA,WAAAA,SAAA;MACA,IAAAC,MAAA,GAAAhC,aAAA,KACA,KAAAnF,UAAA,CACA;MACA,OAAAmH,MAAA,CAAAhH,KAAA;MACA,IAAAiH,MAAA,YAAAA,OAAApG,KAAA;QAAA,OAAAA,KAAA,IAAAqG,KAAA,CAAAC,OAAA,CAAAtG,KAAA,IAAAA,KAAA,CAAAuG,IAAA,UAAApI,SAAA;MAAA;MACA,SAAAH,oBAAA,CAAAgG,MAAA;QACAmC,MAAA,iBAAAC,MAAA,MAAApI,oBAAA;MACA;MACA,SAAAI,sBAAA,CAAA4F,MAAA;QACAmC,MAAA,mBAAAC,MAAA,MAAAhI,sBAAA;MACA;MACA+H,MAAA,gBAAAC,MAAA,MAAApH,UAAA,CAAAsE,OAAA;MACA6C,MAAA,cAAAC,MAAA,MAAApH,UAAA,CAAAG,KAAA;MACAgH,MAAA,WAAAC,MAAA,MAAApH,UAAA,CAAAI,IAAA;MACA,OAAA+G,MAAA;IACA;IACAK,UAAA,WAAAA,WAAAzH,IAAA;MACA,IAAA7C,OAAA,CAAA6C,IAAA;QACA,KAAAC,UAAA,CAAAC,SAAA;QACA,KAAAD,UAAA,CAAAE,OAAA;MACA;QACA,KAAAF,UAAA,CAAAC,SAAA,GAAAF,IAAA;QACA,KAAAC,UAAA,CAAAE,OAAA,GAAAH,IAAA;MACA;IACA;IACA;IACAiD,YAAA,WAAAA,aAAA;MAAA,IAAAyE,MAAA;MACA,IAAAN,MAAA,QAAAD,QAAA;MACA,KAAAxI,OAAA;MACAnB,QAAA,CAAAmK,QAAA,CAAAP,MAAA,EAAAhB,IAAA,WAAA3C,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA;UACAiE,MAAA,CAAAjH,SAAA,GAAAgD,GAAA,CAAA1F,IAAA,CAAA6J,OAAA;UACAF,MAAA,CAAAzH,UAAA,CAAA4H,KAAA,GAAAC,QAAA,CAAArE,GAAA,CAAA1F,IAAA,CAAA8J,KAAA;QACA;MACA,GAAAvB,OAAA;QACAoB,MAAA,CAAA/I,OAAA;MACA;IACA;IACAoJ,WAAA,WAAAA,YAAA;MACA;MACA,KAAArE,KAAA,kBAAAsE,WAAA;MACA,KAAA/H,UAAA,CAAAG,KAAA;MACA,KAAAH,UAAA,CAAAI,IAAA;MACA,KAAAJ,UAAA,CAAAC,SAAA;MACA,KAAAD,UAAA,CAAAE,OAAA;MACA,KAAAH,IAAA;MACA,KAAAoE,aAAA;IACA;IACAA,aAAA,WAAAA,cAAA;MAAA,IAAA6D,QAAA,GAAAjD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA5F,SAAA,GAAA4F,SAAA;MACA,IAAAiD,QAAA;QACA,KAAAhJ,oBAAA;QACA,IAAAyH,uBAAA,QAAAhD,KAAA,CAAAgD,uBAAA;QACA,IAAAA,uBAAA,IAAAA,uBAAA,CAAAM,aAAA;UACAN,uBAAA,CAAAM,aAAA;QACA;QACA,KAAA3H,sBAAA;QACA,IAAA6H,yBAAA,QAAAxD,KAAA,CAAAwD,yBAAA;QACA,IAAAA,yBAAA,IAAAA,yBAAA,CAAAF,aAAA;UACAE,yBAAA,CAAAF,aAAA;QACA;MACA;MACA,KAAA7H,mBAAA,GAAAC,SAAA;MACA,KAAAF,mBAAA;MACA,KAAAK,qBAAA,GAAAH,SAAA;MACA,KAAAE,qBAAA;IACA;IACA4I,UAAA,WAAAA,WAAAC,OAAA;MACA,KAAAlI,UAAA,CAAAM,IAAA,GAAA4H,OAAA;MACA,KAAAlF,YAAA;IACA;IACAmF,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAApI,UAAA,CAAAO,KAAA,GAAA6H,IAAA;MACA,KAAApF,YAAA;IACA;IACAqF,cAAA,WAAAA,eAAAC,SAAA;MACA,KAAAzI,UAAA,GAAAyI,SAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAA9J,OAAA;MACA,IAAAyI,MAAA;MACAA,MAAA;MACA5J,QAAA,CAAAgL,YAAA,CAAApB,MAAA;QACAqB,MAAA,CAAA9J,OAAA;MACA;IACA;IACA+J,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,GAAA,QAAA9I,UAAA,CAAA8E,GAAA,WAAAiE,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAArB,IAAA;MACA,KAAAoB,GAAA;QACA,KAAAvC,QAAA,CAAA9C,KAAA;QACA;MACA;MACA,IAAAuF,aAAA,QAAAhJ,UAAA,CAAA2E,MAAA,WAAAoE,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAAjE,GAAA,WAAAiE,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAArB,IAAA;MACA,KAAAxD,MAAA,CAAAiC,OAAA;QACA5H,KAAA;QACA4F,OAAA,sBAAA6E,aAAA,qBAAAA,aAAA;QACA5C,IAAA,WAAAA,KAAA;UACAyC,MAAA,CAAAhK,OAAA;UACAnB,QAAA,CAAA2I,MAAA;YAAAyC,GAAA,EAAAA;UAAA,GAAAxC,IAAA,WAAA3C,GAAA;YACA,IAAAA,GAAA;cACAkF,MAAA,CAAArF,QAAA,CAAAyF,OAAA;cACAJ,MAAA,CAAA1F,YAAA;YACA;cACA0F,MAAA,CAAArF,QAAA,CAAAC,KAAA,CAAAE,GAAA;YACA;UACA,GAAA6C,OAAA;YAAA,OAAAqC,MAAA,CAAAhK,OAAA;UAAA;QACA;MACA;IACA;IACAqK,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAtK,OAAA;MACA,IAAAyI,MAAA,QAAAD,QAAA;MACAC,MAAA,4BAAA8B,IAAA,GAAAC,OAAA;MACA3L,QAAA,CAAA4L,UAAA,CAAAhC,MAAA;QACA6B,MAAA,CAAAtK,OAAA;MACA;IACA;IACA0K,WAAA,WAAAA,YAAA;MACA,KAAApH,WAAA;MACA,IAAA8D,eAAA,QAAArC,KAAA,CAAAqC,eAAA;MACA,IAAAA,eAAA,EAAAA,eAAA,CAAAvB,UAAA;IACA;IACA8E,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,IAAAX,GAAA,QAAA9I,UAAA,CAAA8E,GAAA,WAAAiE,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAArB,IAAA;MACA,KAAAoB,GAAA;QACA,KAAAvC,QAAA,CAAA9C,KAAA;QACA;MACA;MACA,KAAA5E,OAAA;MACAnB,QAAA,CAAAgM,YAAA;QAAAZ,GAAA,EAAAA;MAAA,GAAAxC,IAAA,WAAA3C,GAAA;QACA,IAAAA,GAAA;UACA8F,MAAA,CAAAjG,QAAA,CAAAyF,OAAA;QACA;UACAQ,MAAA,CAAAjG,QAAA,CAAAC,KAAA,CAAAE,GAAA;QACA;MACA,GAAA6C,OAAA;QAAA,OAAAiD,MAAA,CAAA5K,OAAA;MAAA;IACA;IACA8K,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAd,GAAA,QAAA9I,UAAA,CAAA8E,GAAA,WAAAiE,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAArB,IAAA;MACA,KAAA7I,OAAA;MACAnB,QAAA,CAAAiM,aAAA;QAAAb,GAAA,EAAAA;MAAA,GAAAxC,IAAA,WAAA3C,GAAA;QACA,IAAAA,GAAA;UACAiG,MAAA,CAAApG,QAAA,CAAAyF,OAAA;QACA;UACAW,MAAA,CAAApG,QAAA,CAAAC,KAAA,CAAAE,GAAA;QACA;MACA,GAAA6C,OAAA;QAAA,OAAAoD,MAAA,CAAA/K,OAAA;MAAA;IACA;IACA;IACAgL,WAAA,WAAAA,YAAA3I,GAAA;MAAA,IAAA4I,MAAA;MACA,KAAA5F,MAAA,CAAAiC,OAAA;QACA5H,KAAA;QACA4F,OAAA,4CAAA4F,MAAA,CAAA7I,GAAA,CAAAuB,GAAA;QACA2D,IAAA,WAAAA,KAAA;UACA0D,MAAA,CAAAE,IAAA,CAAA9I,GAAA;UACA;UACAxD,QAAA,CAAAuM,KAAA;YAAAzH,EAAA,EAAAtB,GAAA,CAAAsB;UAAA,GAAA8D,IAAA,WAAA3C,GAAA;YACA,IAAAA,GAAA,IAAAA,GAAA,CAAAG,IAAA;cACAgG,MAAA,CAAAtG,QAAA,CAAAyF,OAAA;cACAa,MAAA,CAAA3G,YAAA;YACA;cACA2G,MAAA,CAAAtG,QAAA,CAAAC,KAAA,CAAAE,GAAA,CAAAb,OAAA;YACA;UACA,GAAAoH,KAAA,WAAA5G,GAAA;YACAwG,MAAA,CAAAtG,QAAA,CAAAC,KAAA,cAAAH,GAAA,CAAAR,OAAA;UACA,GAAA0D,OAAA;YACAsD,MAAA,CAAAE,IAAA,CAAA9I,GAAA;UACA;QACA;MACA;IACA;IACA;IACAiJ,YAAA,WAAAA,aAAAjJ,GAAA;MACA;MACA,KAAAkJ,eAAA,CAAAlJ,GAAA;IACA;IAEAkJ,eAAA,WAAAA,gBAAAlJ,GAAA;MACA,KAAAqB,UAAA;QACAC,EAAA,EAAAtB,GAAA,CAAAsB,EAAA;QACAC,GAAA,EAAAvB,GAAA,CAAAuB,GAAA;QACAC,KAAA;QAAA;QACAC,OAAA;MACA;;MACA,KAAAN,kBAAA;IACA;IAEAgI,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAA1G,KAAA,CAAArB,UAAA,CAAAgI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,OAAA,CAAAhI,aAAA;UACA,IAAAgF,MAAA;YACA9E,EAAA,EAAA8H,OAAA,CAAA/H,UAAA,CAAAC,EAAA;YACAiI,SAAA;cACA/H,KAAA,EAAA4H,OAAA,CAAA/H,UAAA,CAAAG,KAAA;cAAA;cACAC,OAAA,EAAA2H,OAAA,CAAA/H,UAAA,CAAAI;YACA;UACA;UAEAhF,QAAA,CAAA+M,MAAA,CAAApD,MAAA,EAAAhB,IAAA,WAAA3C,GAAA;YACA,IAAAA,GAAA,IAAAA,GAAA,CAAAG,IAAA;cACAwG,OAAA,CAAA9G,QAAA,CAAAyF,OAAA;cACAqB,OAAA,CAAAjI,kBAAA;cACAiI,OAAA,CAAAnH,YAAA;YACA;cACAmH,OAAA,CAAA9G,QAAA,CAAAC,KAAA,CAAAE,GAAA,CAAAb,OAAA;YACA;UACA,GAAAoH,KAAA,WAAA5G,GAAA;YACAgH,OAAA,CAAA9G,QAAA,CAAAC,KAAA,cAAAH,GAAA,CAAAR,OAAA;UACA,GAAA0D,OAAA;YACA8D,OAAA,CAAAhI,aAAA;UACA;QACA;MACA;IACA;IAEAqI,kBAAA,WAAAA,mBAAA;MACA,KAAAtI,kBAAA;MACA,KAAAE,UAAA;QACAC,EAAA;QACAC,GAAA;QACAC,KAAA;QAAA;QACAC,OAAA;MACA;IACA;EACA;AACA"}]}