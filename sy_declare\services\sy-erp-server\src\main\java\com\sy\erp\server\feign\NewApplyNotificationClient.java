package com.sy.erp.server.feign;

import com.aimo.common.model.ResultBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 新申请通知Feign客户端
 * 用于调用aimo-base-server的通知接口
 * 
 * <AUTHOR>
 */
@FeignClient(name = "newApplyNotificationClient", url = "${aimo.base.server.url:http://localhost:8233}", fallback = NewApplyNotificationClientFallback.class)
public interface NewApplyNotificationClient {

    /**
     * 审批流通知接口
     *
     * @param userIds 用户ID列表，逗号分隔
     * @param title 通知标题
     * @param content 通知内容
     * @return 通知发送结果
     */
    @PostMapping(value = "/newApply/noticeUsers")
    ResultBody<Boolean> noticeUsers(@RequestParam(value = "userIds") String userIds,
                                   @RequestParam(value = "title") String title,
                                   @RequestParam(value = "content") String content);
}
