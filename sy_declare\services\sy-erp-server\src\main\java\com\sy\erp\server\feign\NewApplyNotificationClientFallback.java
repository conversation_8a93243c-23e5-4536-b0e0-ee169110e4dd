package com.sy.erp.server.feign;

import com.aimo.common.model.ResultBody;
import org.springframework.stereotype.Component;

/**
 * NewApplyNotificationClient的降级处理类
 * 
 * <AUTHOR>
 */
@Component
public class NewApplyNotificationClientFallback implements AiMoBaseClient {

    @Override
    public ResultBody<Boolean> noticeUsers(String userIds, String title, String content) {
        System.err.println("调用aimo-base-server的noticeUsers接口失败，使用降级处理");
        System.err.println("参数: userIds=" + userIds + ", title=" + title + ", content=" + content);
        
        // 返回失败结果
        return ResultBody.failed(false, 500, "服务暂时不可用，请稍后重试");
    }
}
