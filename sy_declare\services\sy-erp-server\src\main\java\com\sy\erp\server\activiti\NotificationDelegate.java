package com.sy.erp.server.activiti;

import com.aimo.common.model.ResultBody;
import com.sy.erp.server.controller.ErpWorkflowController;
import com.sy.erp.server.feign.AiMoBaseClient;
import com.sy.erp.server.service.WorkflowService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.Expression;
import org.activiti.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class NotificationDelegate implements JavaDelegate {
    // 字段名必须与XML配置完全一致（包括大小写）
    private Expression recipients;
    private Expression notificationTitle;
    private Expression notificationContent;

    @Autowired
    private WorkflowService workflowService;

    @PostConstruct
    public void init() {
        System.out.println("NotificationDelegate 初始化完成");
        System.out.println("NewApplyNotificationClient 注入状态: " + (workflowService != null ? "成功" : "失败"));
    }

    @Override
    public void execute(DelegateExecution execution) {
        String userIds = recipients.getValue(execution).toString();
        String title = notificationTitle.getValue(execution).toString();
        String content = notificationContent.getValue(execution).toString();
        System.out.println("发送通知给: " + userIds);
        System.out.println("标题: " + title);
        System.out.println("内容: " + content);
        try {
            ResultBody<Boolean> resultBody = workflowService.getAimoNotification(userIds, title, content);
            if (resultBody != null && resultBody.isOk() && resultBody.getData() != null && resultBody.getData()) {
                System.out.println("通知发送成功");
                // TODO: noticeUsers return true后需要变更业务的状态:status为6
            } else {
                System.out.println("通知发送失败: " + (resultBody != null ? resultBody.getMessage() : "未知错误"));
            }
        } catch (Exception e) {
            System.err.println("调用通知接口失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
